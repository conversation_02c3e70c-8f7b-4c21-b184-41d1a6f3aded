package com.tongji.logic.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 任务结果
 * </p>
 *
 * <AUTHOR>
 */
@Schema(description = "任务结果")
@Data
public class TaskResult {

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "任务处理进度[8=处理成功;9=处理失败]")
    private Integer status;

    @Schema(description = "结果处理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processStartTime;

    @Schema(description = "结果处理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processEndTime;

    @Schema(description = "结果失败原因,progress=3时有效")
    private String failReason;

    @Schema(description = "指标结果")
    private List<TaskIndicator> indicators;

}
