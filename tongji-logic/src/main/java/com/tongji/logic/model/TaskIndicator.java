package com.tongji.logic.model;

import com.tongji.logic.entity.indicator.TIndicator;
import com.tongji.logic.entity.result.TResultItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 任务指标
 * </p>
 *
 * <AUTHOR>
 */
@Schema(description = "任务指标")
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskIndicator extends TIndicator {

    @Schema(description = "指标结果数量")
    private Integer amount;

    @Schema(description = "指标结果条目")
    private List<TResultItem> items;

}
