package com.tongji.logic.dto.req.task;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <p>
 * 添加数据分析任务请求
 * </p>
 */
@Data
@Schema(description = "添加数据分析任务请求")
public class TaskAddReq {

    @Schema(description = "账页表单ID")
    @NotBlank(message = "账页表单ID不能为空")
    private Long ledgerSheetId;

    @Schema(description = "备注")
    private String remark;
}
