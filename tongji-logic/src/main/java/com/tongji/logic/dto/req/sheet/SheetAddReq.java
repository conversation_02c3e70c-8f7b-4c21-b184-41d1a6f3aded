package com.tongji.logic.dto.req.sheet;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <p>
 * 添加表单请求
 * </p>
 */
@Data
@Schema(description = "添加表单请求")
public class SheetAddReq {

    @Schema(description = "工作表类型[1=A卷;2=B卷;11=月度账页]，A卷和B卷只能同时存在一条未删除的记录，月度账页每个月只能同时存在一条未删除的记录")
    @NotNull(message = "工作表类型不能为空")
    private Integer type;

    @Schema(description = "账页日期,type=11时有效", example = "2023-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ledgerDate;

    @Schema(description = "文件路径,文件路径在记录创建时就需要指定,如果修改需要先删除记录再添加新的记录,以保证任务的数据一致性")
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    @Schema(description = "备注")
    private String remark;
}
