package com.tongji.logic.dto.req.sheet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@Schema(description = "表单查询请求")
public class SheetListReq {

    @Schema(description = "工作表类型[1=A卷;2=B卷;11=月度账页]")
    private Integer type;

    @Schema(description = "账页日期,type=11时有效", example = "2023-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate ledgerDate;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginCreateTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endCreateTime;

}
