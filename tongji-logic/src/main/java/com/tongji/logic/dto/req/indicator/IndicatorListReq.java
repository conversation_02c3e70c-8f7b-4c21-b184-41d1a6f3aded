package com.tongji.logic.dto.req.indicator;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Schema(description = "指标查询请求")
public class IndicatorListReq {

    @Schema(description = "适用审核类型[1=逐月审核]")
    private Integer auditType;

    @Schema(description = "指标名称")
    private String name;

    @Schema(description = "指标标识")
    private String symbol;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginCreateTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endCreateTime;
}
