package com.tongji.logic.dto.req.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Schema(description = "任务查询请求")
public class TaskListReq {

    @Schema(description = "审核类型[month=逐月审核]")
    private String auditType;

    @Schema(description = "任务处理进度[0=等待中;1=处理中;8=处理成功;9=处理失败]")
    private Integer progress;

    @Schema(description = "创建人")
    private Long createUserId;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginCreateTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endCreateTime;
}
