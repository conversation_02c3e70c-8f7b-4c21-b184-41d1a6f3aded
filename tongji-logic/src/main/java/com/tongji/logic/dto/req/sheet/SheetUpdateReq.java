package com.tongji.logic.dto.req.sheet;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <p>
 * 修改表单请求
 * </p>
 */
@Data
@Schema(description = "修改表单请求")
public class SheetUpdateReq {

    @Schema(description = "表单ID")
    @NotNull(message = "表单ID不能为空")
    private Long id;

    @Schema(description = "账页日期,type=11时有效", example = "2023-01-01")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "账页日期不能为空")
    private LocalDate ledgerDate;

    @Schema(description = "文件路径,文件路径在记录创建时就需要指定,如果修改需要先删除记录再添加新的记录,以保证任务的数据一致性")
    @NotNull(message = "文件路径不能为空")
    private String filePath;

    @Schema(description = "备注")
    private String remark;
}
