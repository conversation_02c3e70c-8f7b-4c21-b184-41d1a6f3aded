package com.tongji.logic.dto.req.result;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/**
 * <p>
 * 任务结果查询请求
 * </p>
 */
@Data
@Schema(description = "任务结果查询请求")
public class TaskResultQueryReq {

    @Schema(description = "任务ID")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;
    
    @Schema(description = "是否包含详细结果条目[0=否;1=是]", example = "false")
    @Range(min = 0, max = 1, message = "是否包含详细结果条目必须为0或1")
    private Integer includeItems = 0;
}
