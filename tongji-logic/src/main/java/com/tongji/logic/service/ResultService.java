package com.tongji.logic.service;

import com.alibaba.fastjson2.JSON;
import com.tongji.logic.entity.result.TResultItem;
import com.tongji.logic.entity.result.TResultStat;
import com.tongji.logic.entity.task.TTask;
import com.tongji.logic.mapper.result.TResultItemMapper;
import com.tongji.logic.mapper.result.TResultStatMapper;
import com.tongji.logic.mapper.task.TTaskMapper;
import com.tongji.logic.model.TaskIndicator;
import com.tongji.logic.model.TaskResult;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class ResultService {

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private TResultItemMapper resultItemMapper;

    @Resource
    private TResultStatMapper resultStatMapper;


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveResult(TTask task, TaskResult result) {
        System.out.println(JSON.toJSONString(task));
        System.out.println(JSON.toJSONString(result));

        List<TResultStat> stats = new ArrayList<>();
        List<TResultItem> items = new ArrayList<>();

        if(result.getIndicators() != null) {
            for (TaskIndicator indicator : result.getIndicators()) {
                TResultStat stat = new TResultStat();
                stat.setTaskId(task.getId());
                stat.setIndicatorId(indicator.getId());
                stat.setAmount(indicator.getItems().size());
                stats.add(stat);

                if(indicator.getItems() != null) {
                    for (TResultItem item : indicator.getItems()) {
                        item.setTaskId(task.getId());
                        item.setIndicatorId(indicator.getId());
                        items.add(item);
                    }
                }
            }
        }

        resultStatMapper.insert(stats);
        resultItemMapper.insert(items);

        task.setProgress(result.getStatus());
        task.setProcessStartTime(result.getProcessStartTime());
        task.setProcessEndTime(result.getProcessEndTime());
        task.setFailReason(result.getFailReason());
        task.setUpdateTime(LocalDateTime.now());
        task.setUpdateBy("system");
        taskMapper.updateById(task);
    }

}
