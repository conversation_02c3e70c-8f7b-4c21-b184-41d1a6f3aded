package com.tongji.logic.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tongji.common.exception.ServiceException;
import com.tongji.logic.dto.req.result.TaskResultQueryReq;
import com.tongji.logic.entity.indicator.TIndicator;
import com.tongji.logic.entity.result.TResultItem;
import com.tongji.logic.entity.result.TResultStat;
import com.tongji.logic.entity.task.TTask;
import com.tongji.logic.mapper.indicator.TIndicatorMapper;
import com.tongji.logic.mapper.result.TResultItemMapper;
import com.tongji.logic.mapper.result.TResultStatMapper;
import com.tongji.logic.mapper.task.TTaskMapper;
import com.tongji.logic.model.TaskIndicator;
import com.tongji.logic.model.TaskResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Tag(name = "任务结果", description = "任务结果相关接口")
public class ResultService {

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private TResultItemMapper resultItemMapper;

    @Resource
    private TResultStatMapper resultStatMapper;

    @Resource
    private TIndicatorMapper indicatorMapper;


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveResult(TTask task, TaskResult result) {
        List<TResultStat> stats = new ArrayList<>();
        List<TResultItem> items = new ArrayList<>();

        if(result.getIndicators() != null) {
            for (TaskIndicator indicator : result.getIndicators()) {
                TResultStat stat = new TResultStat();
                stat.setTaskId(task.getId());
                stat.setIndicatorId(indicator.getId());
                stat.setAmount(indicator.getItems().size());
                stat.setCreateTime(LocalDateTime.now());
                stats.add(stat);

                if(indicator.getItems() != null) {
                    for (TResultItem item : indicator.getItems()) {
                        item.setTaskId(task.getId());
                        item.setIndicatorId(indicator.getId());
                        items.add(item);
                    }
                }
            }
        }

        resultStatMapper.insert(stats);
        resultItemMapper.insert(items);

        task.setProgress(result.getStatus());
        task.setProcessStartTime(result.getProcessStartTime());
        task.setProcessEndTime(result.getProcessEndTime());
        task.setFailReason(result.getFailReason());
        task.setUpdateTime(LocalDateTime.now());
        task.setUpdateBy("system");
        taskMapper.updateById(task);
    }

    /**
     * 查询任务结果
     *
     * @param queryReq 查询请求
     * @return 任务结果
     */
    @Operation(summary = "查询任务结果", description = "根据任务ID查询任务结果")
    public TaskResult queryTaskResult(TaskResultQueryReq queryReq) {
        // 查询任务信息
        TTask task = taskMapper.selectById(queryReq.getTaskId());
        if (task == null || task.getDeleteFlag() == 1) {
            throw new ServiceException("任务不存在");
        }

        // 只有处理成功或处理失败的任务才有结果
        if (task.getProgress() != 8 && task.getProgress() != 9) {
            throw new ServiceException("任务尚未处理完成，无法查询结果");
        }

        // 构建任务结果对象
        TaskResult result = new TaskResult();
        result.setTaskId(task.getId());
        result.setStatus(task.getProgress());
        result.setProcessStartTime(task.getProcessStartTime());
        result.setProcessEndTime(task.getProcessEndTime());
        result.setFailReason(task.getFailReason());

        // 查询任务结果统计信息
        LambdaQueryWrapper<TResultStat> statWrapper = Wrappers.lambdaQuery();
        statWrapper.eq(TResultStat::getTaskId, task.getId());
        List<TResultStat> stats = resultStatMapper.selectList(statWrapper);

        if (stats != null && !stats.isEmpty()) {
            // 获取所有相关的指标ID
            List<Long> indicatorIds = stats.stream()
                    .map(TResultStat::getIndicatorId)
                    .distinct()
                    .collect(Collectors.toList());

            // 查询指标信息
            LambdaQueryWrapper<TIndicator> indicatorWrapper = Wrappers.lambdaQuery();
            indicatorWrapper.in(TIndicator::getId, indicatorIds);
            List<TIndicator> indicators = indicatorMapper.selectList(indicatorWrapper);

            // 将指标信息转换为Map，方便查找
            Map<Long, TIndicator> indicatorMap = indicators.stream()
                    .collect(Collectors.toMap(TIndicator::getId, indicator -> indicator));

            // 构建指标结果列表
            List<TaskIndicator> indicatorResults = new ArrayList<>();

            // 如果需要包含详细结果条目
            if (queryReq.getIncludeItems() == 1) {
                // 查询所有结果条目
                LambdaQueryWrapper<TResultItem> itemWrapper = Wrappers.lambdaQuery();
                itemWrapper.eq(TResultItem::getTaskId, task.getId());
                List<TResultItem> items = resultItemMapper.selectList(itemWrapper);

                // 按指标ID分组
                Map<Long, List<TResultItem>> itemMap = items.stream()
                        .collect(Collectors.groupingBy(TResultItem::getIndicatorId));

                // 构建每个指标的结果
                for (TResultStat stat : stats) {
                    Long indicatorId = stat.getIndicatorId();
                    TIndicator indicator = indicatorMap.get(indicatorId);

                    if (indicator != null) {
                        TaskIndicator taskIndicator = new TaskIndicator();
                        BeanUtils.copyProperties(indicator, taskIndicator);
                        taskIndicator.setAmount(stat.getAmount());

                        // 设置结果条目
                        List<TResultItem> indicatorItems = itemMap.getOrDefault(indicatorId, new ArrayList<>());
                        taskIndicator.setItems(indicatorItems);

                        indicatorResults.add(taskIndicator);
                    }
                }
            } else {
                // 不包含详细结果条目，只返回指标基本信息和统计数量
                for (TResultStat stat : stats) {
                    Long indicatorId = stat.getIndicatorId();
                    TIndicator indicator = indicatorMap.get(indicatorId);

                    if (indicator != null) {
                        TaskIndicator taskIndicator = new TaskIndicator();
                        BeanUtils.copyProperties(indicator, taskIndicator);
                        taskIndicator.setAmount(stat.getAmount());

                        // 设置空的结果条目列表
                        taskIndicator.setItems(new ArrayList<>());

                        indicatorResults.add(taskIndicator);
                    }
                }
            }

            result.setIndicators(indicatorResults);
        } else {
            // 没有结果统计信息，设置空的指标列表
            result.setIndicators(new ArrayList<>());
        }

        return result;
    }

}
