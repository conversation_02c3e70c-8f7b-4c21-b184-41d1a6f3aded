package com.tongji.logic.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.logic.dto.req.sheet.SheetAddReq;
import com.tongji.logic.dto.req.sheet.SheetListReq;
import com.tongji.logic.entity.task.TSheet;
import com.tongji.logic.mapper.task.TSheetMapper;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 表单表 服务类
 * </p>
 */
@Service
@Tag(name = "表单管理", description = "表单管理相关接口")
public class SheetService {

    @Resource
    private TSheetMapper sheetMapper;

    /**
     * 添加表单记录
     *
     * @param sheetAddReq 添加表单请求
     * @param loginUser 登录用户
     * @return 添加结果
     */
    @Operation(summary = "添加表单记录", description = "添加表单记录")
    @Transactional(rollbackFor = Exception.class)
    public int addSheet(SheetAddReq sheetAddReq, SysUser loginUser) {
        // 校验表单类型
        if (sheetAddReq.getType() != 1 && sheetAddReq.getType() != 2 && sheetAddReq.getType() != 11) {
            throw new ServiceException("表单类型不正确");
        }

        // 如果是月度账页，需要校验账页日期
        if (sheetAddReq.getType() == 11 && sheetAddReq.getLedgerDate() == null) {
            throw new ServiceException("账页日期不能为空");
        }

        // 创建表单记录
        TSheet sheet = new TSheet();
        BeanUtils.copyProperties(sheetAddReq, sheet);

        // 检查是否已存在相同类型的记录
        if (sheetAddReq.getType() == 1 || sheetAddReq.getType() == 2) {
            // A卷和B卷只能同时存在一条未删除的记录
            TSheet existSheet = sheetMapper.selectFirst(q -> q
                    .eq(TSheet::getType, sheetAddReq.getType())
                    .eq(TSheet::getDeleteFlag, 0)
            );
            if (existSheet != null) {
                throw new ServiceException("已存在相同类型的表单记录");
            }
        } else if (sheetAddReq.getType() == 11) {
            LocalDate ledgerDate = sheetAddReq.getLedgerDate().withDayOfMonth(1);

            // 月度账页每个月只能同时存在一条未删除的记录
            TSheet existSheet = sheetMapper.selectFirst(q -> q
                    .eq(TSheet::getType, 11)
                    .eq(TSheet::getLedgerDate, ledgerDate)
                    .eq(TSheet::getDeleteFlag, 0)
            );
            if (existSheet != null) {
                throw new ServiceException("已存在相同月份的账页记录");
            }

            sheet.setLedgerDate(ledgerDate);
        }

        // 设置创建人信息
        sheet.setCreateBy(loginUser.getUsername());
        sheet.setCreateUserId(loginUser.getUserId());
        sheet.setUpdateBy(loginUser.getUsername());
        sheet.setUpdateTime(LocalDateTime.now());
        sheet.setDeleteFlag(0); // 设置为未删除

        return sheetMapper.insert(sheet);
    }

    /**
     * 查询表单分页列表
     *
     * @param listReq 查询条件
     * @return 表单列表
     */
    @Operation(summary = "查询表单分页列表", description = "根据条件查询表单分页列表")
    public TablePage<TSheet> queryPage(SheetListReq listReq) {
        return PageUtils.paginate(() -> {
            LambdaQueryWrapper<TSheet> wrapper = Wrappers.lambdaQuery();

            wrapper.eq(TSheet::getDeleteFlag, 0);

            // 设置查询条件
            if (listReq.getType() != null) {
                wrapper.eq(TSheet::getType, listReq.getType());
            }

            if (listReq.getLedgerDate() != null) {
                wrapper.eq(TSheet::getLedgerDate, listReq.getLedgerDate());
            }

            if (StringUtils.isNotEmpty(listReq.getBeginCreateTime())) {
                wrapper.ge(TSheet::getCreateTime, listReq.getBeginCreateTime());
            } else if (StringUtils.isNotEmpty(listReq.getEndCreateTime())) {
                wrapper.le(TSheet::getCreateTime, listReq.getEndCreateTime());
            }

            // 按创建时间降序排序
            wrapper.orderByDesc(TSheet::getLedgerDate).orderByDesc(TSheet::getCreateTime);

            return sheetMapper.selectList(wrapper);
        });
    }

    /**
     * 获取表单详情
     *
     * @param sheetId 表单ID
     * @return 表单详情
     */
    @Operation(summary = "获取表单详情", description = "根据表单ID获取表单详情")
    public TSheet queryDetail(Long sheetId) {
        return sheetMapper.selectById(sheetId);
    }
}
