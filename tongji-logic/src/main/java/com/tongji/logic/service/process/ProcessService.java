package com.tongji.logic.service.process;

import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class ProcessService {

    protected Boolean checkColumnExisted(List<JSONObject> jsonList, String columnName) {
        if(jsonList.isEmpty()) {
            return false;
        }

        return jsonList.get(0).containsKey(columnName);
    }

    protected Map<String, List<JSONObject>> groupByColumn(List<JSONObject> objects, String columnName) {
        Map<String, List<JSONObject>> map = new HashMap<>();
        objects.forEach(json -> {
                    String name = json.getOrDefault(columnName, "").toString().trim();
                    map.computeIfAbsent(name, k -> new ArrayList<>()).add(json);
                });

        return map;
    }

    /**
     * 根据列名获取该列去重后的数据集合
     */
    protected Set<String> collectByColumn(List<JSONObject> objects, String columnName) {
        return objects.stream()
                .map(json -> json.getOrDefault(columnName, "").toString().trim())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 计算账页金额
     */
    protected BigDecimal calcLedgerAmount(List<JSONObject> objects) {
        return objects.stream().map(json -> {
            String money = json.getString("MONEY金额");
            // 判断金额是否为空
            if(StringUtils.isBlank(money)) {
                return BigDecimal.ZERO;
            }

            return new BigDecimal(money.trim());
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 根据户号统计A卷人数
     */
    protected long countMemberOfFamily(List<JSONObject> objects, String sCode) {
        return objects.stream()
                .filter(json -> sCode.equalsIgnoreCase(json.getOrDefault("SCODE", "").toString().trim()))
                .filter(json -> "1".equalsIgnoreCase(json.getOrDefault("A119", "").toString().trim()))
                .count();
    }

    /**
     * 过滤账页
     */
    protected List<JSONObject> filterLedger(List<JSONObject> objects, String sCode, String personCode, List<String> codes) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE样本编码", "").toString().trim()))
                .filter(json2 -> personCode.equalsIgnoreCase(json2.getOrDefault("PERSON人码", "").toString().trim()))
                .filter(json2 -> codes.contains(json2.getOrDefault("CODE编码", "").toString().trim()))
                .toList();
    }

    /**
     * 过滤账页
     */
    protected List<JSONObject> filterLedger(List<JSONObject> objects, String sCode, List<String> codes) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE样本编码", "").toString().trim()))
                .filter(json2 -> codes.contains(json2.getOrDefault("CODE编码", "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterLedger(List<JSONObject> objects, String sCode, Function<String, Boolean> codeFilter) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE样本编码", "").toString().trim()))
                .filter(json2 -> {
                    String code = json2.getString("CODE编码");
                    if (StringUtils.isBlank(code)) {
                        return false;
                    }
                    return codeFilter.apply(code);
                })
                .toList();
    }

    protected List<JSONObject> filterMember(List<JSONObject> objects, String sCode) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterMember(List<JSONObject> objects, String sCode, String columnName, String columnValue) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .filter(json2 -> columnValue.equalsIgnoreCase(json2.getOrDefault(columnName, "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterMember(List<JSONObject> objects, String sCode, String personCode) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .filter(json2 -> personCode.equalsIgnoreCase(json2.getOrDefault("A100", "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterMember(List<JSONObject> objects, String sCode, String personCode, String columnName, String columnValue) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .filter(json2 -> personCode.equalsIgnoreCase(json2.getOrDefault("A100", "").toString().trim()))
                .filter(json2 -> columnValue.equalsIgnoreCase(json2.getOrDefault(columnName, "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterFamily(List<JSONObject> objects, String sCode) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .toList();
    }

    protected List<JSONObject> filterFamily(List<JSONObject> objects, String sCode, String columnName, String columnValue) {
        return objects.stream()
                .filter(json2 -> sCode.equalsIgnoreCase(json2.getOrDefault("SCODE", "").toString().trim()))
                .filter(json2 -> columnValue.equalsIgnoreCase(json2.getOrDefault(columnName, "").toString().trim()))
                .toList();
    }
}
