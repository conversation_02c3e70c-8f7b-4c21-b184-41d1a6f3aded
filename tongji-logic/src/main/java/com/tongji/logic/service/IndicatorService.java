package com.tongji.logic.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.logic.dto.req.indicator.IndicatorListReq;
import com.tongji.logic.entity.indicator.TIndicator;
import com.tongji.logic.mapper.indicator.TIndicatorMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据分析指标 服务类
 * </p>
 */
@Service
@Tag(name = "数据分析指标", description = "数据分析指标相关接口")
public class IndicatorService {

    @Resource
    private TIndicatorMapper indicatorMapper;

    /**
     * 查询指标列表
     *
     * @param listReq 查询条件
     * @return 指标列表
     */
    @Operation(summary = "查询指标列表", description = "根据条件查询指标列表")
    public TablePage<TIndicator> queryPage(IndicatorListReq listReq) {
        return PageUtils.paginate(() -> {
            LambdaQueryWrapper<TIndicator> wrapper = Wrappers.lambdaQuery();

            wrapper.eq(TIndicator::getDeleteFlag, 0);

            if (listReq.getAuditType() != null) {
                wrapper.eq(TIndicator::getAuditType, listReq.getAuditType());
            }

            if (StringUtils.isNotEmpty(listReq.getName())) {
                wrapper.like(TIndicator::getName, listReq.getName());
            }

            if (StringUtils.isNotEmpty(listReq.getSymbol())) {
                wrapper.like(TIndicator::getSymbol, listReq.getSymbol());
            }

            if (StringUtils.isNotEmpty(listReq.getCreateBy())) {
                wrapper.like(TIndicator::getCreateBy, listReq.getCreateBy());
            }

            if (StringUtils.isNotEmpty(listReq.getBeginCreateTime()) && StringUtils.isNotEmpty(listReq.getEndCreateTime())) {
                wrapper.between(TIndicator::getCreateTime, listReq.getBeginCreateTime(), listReq.getEndCreateTime());
            } else if (StringUtils.isNotEmpty(listReq.getBeginCreateTime())) {
                wrapper.ge(TIndicator::getCreateTime, listReq.getBeginCreateTime());
            } else if (StringUtils.isNotEmpty(listReq.getEndCreateTime())) {
                wrapper.le(TIndicator::getCreateTime, listReq.getEndCreateTime());
            }

            // 按创建时间降序排序
            wrapper.orderByDesc(TIndicator::getCreateTime);

            return indicatorMapper.selectList(wrapper);
        });
    }

    /**
     * 获取指标详情
     *
     * @param indicatorId 指标ID
     * @return 指标详情
     */
    @Operation(summary = "获取指标详情", description = "根据指标ID获取指标详情")
    public TIndicator queryDetail(Long indicatorId) {
        TIndicator indicator = indicatorMapper.selectById(indicatorId);
        if (indicator == null || indicator.getDeleteFlag() == 1) {
            throw new ServiceException("指标不存在");
        }

        return indicator;
    }
}
