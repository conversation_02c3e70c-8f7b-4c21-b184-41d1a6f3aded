package com.tongji.logic.service.process;

import com.alibaba.fastjson2.JSONObject;
import com.tongji.common.exception.ServiceException;
import com.tongji.logic.entity.result.TResultItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MonthProcessService extends ProcessService {

    public void checkColumnsExisted(
            List<JSONObject> memberList, List<JSONObject> familyList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        if(!checkColumnExisted(memberList, "SCODE")) {
            throw new ServiceException("A卷文件中未找到'SCODE'列");
        }

        if(!checkColumnExisted(memberList, "A100")) {
            throw new ServiceException("A卷文件中未找到'A100'列");
        }

        if(!checkColumnExisted(memberList, "A101")) {
            throw new ServiceException("A卷文件中未找到'A101'列");
        }

        if(!checkColumnExisted(familyList, "SCODE")) {
            throw new ServiceException("B卷文件中未找到'SCODE'列");
        }

        if(!checkColumnExisted(familyList, "B201")) {
            throw new ServiceException("B卷文件中未找到'B201'列");
        }

        if(!checkColumnExisted(familyList, "B201_1")) {
            throw new ServiceException("B卷文件中未找到'B201_1'列");
        }

        // 本月账页
        if(!checkColumnExisted(ledgerList, "SCODE样本编码")) {
            throw new ServiceException("本月账页文件中未找到'SCODE样本编码'列");
        }

        if(!checkColumnExisted(ledgerList, "YEAR年")) {
            throw new ServiceException("本月账页文件中未找到'YEAR年'列");
        }

        if(!checkColumnExisted(ledgerList, "MONTH月")) {
            throw new ServiceException("本月账页文件中未找到'MONTH月'列");
        }

        if(!checkColumnExisted(ledgerList, "CODE编码")) {
            throw new ServiceException("本月账页文件中未找到'CODE编码'列");
        }

        if(!checkColumnExisted(ledgerList, "MONEY金额")) {
            throw new ServiceException("本月账页文件中未找到'MONEY金额'列");
        }

        if(!checkColumnExisted(ledgerList, "PERSON人码")) {
            throw new ServiceException("本月账页文件中未找到'PERSON人码'列");
        }

        // 上月账页
        if(!checkColumnExisted(prev1LedgerList, "SCODE样本编码")) {
            throw new ServiceException("上月账页文件中未找到'SCODE样本编码'列");
        }

        if(!checkColumnExisted(prev1LedgerList, "YEAR年")) {
            throw new ServiceException("上月账页文件中未找到'YEAR年'列");
        }

        if(!checkColumnExisted(prev1LedgerList, "MONTH月")) {
            throw new ServiceException("上月账页文件中未找到'MONTH月'列");
        }

        if(!checkColumnExisted(prev1LedgerList, "CODE编码")) {
            throw new ServiceException("上月账页文件中未找到'CODE编码'列");
        }

        if(!checkColumnExisted(prev1LedgerList, "MONEY金额")) {
            throw new ServiceException("上月账页文件中未找到'MONEY金额'列");
        }

        if(!checkColumnExisted(prev1LedgerList, "PERSON人码")) {
            throw new ServiceException("上月账页文件中未找到'PERSON人码'列");
        }

        // 上上月账页
        if(!checkColumnExisted(prev2LedgerList, "SCODE样本编码")) {
            throw new ServiceException("上上月账页文件中未找到'SCODE样本编码'列");
        }

        if(!checkColumnExisted(prev2LedgerList, "YEAR年")) {
            throw new ServiceException("上上月账页文件中未找到'YEAR年'列");
        }

        if(!checkColumnExisted(prev2LedgerList, "MONTH月")) {
            throw new ServiceException("上上月账页文件中未找到'MONTH月'列");
        }

        if(!checkColumnExisted(prev2LedgerList, "CODE编码")) {
            throw new ServiceException("上上月账页文件中未找到'CODE编码'列");
        }

        if(!checkColumnExisted(prev2LedgerList, "MONEY金额")) {
            throw new ServiceException("上上月账页文件中未找到'MONEY金额'列");
        }

        if(!checkColumnExisted(prev2LedgerList, "PERSON人码")) {
            throw new ServiceException("上上月账页文件中未找到'PERSON人码'列");
        }
    }

//    /**
//     * 计算上上月和上月都存在相关code的记录，但是在本月没有相关code的记录<br />
//     * 实现方式：
//     * 1. 从上上月账页中找到所有相关code的记录
//     * 2. 从上月账页中找到所有相关code的记录
//     * 3. 找到同时在上上月和上月都存在的记录
//     * 4. 找到同时在上上月和上月都存在的记录，但是在本月没有相关code的记录
//     * 5. 从A卷中找到户主姓名
//     * 6. 组装结果
//     */
//    public List<TResultItem> calcOmissionByLastTwoMonths(
//            JSONArray memberList, JSONArray ledgerList,
//            JSONArray prev1LedgerList, JSONArray prev2LedgerList,
//            List<String> codes, String subject
//    ) {
//        // 上上个月家庭户中人员有相关code的记录
//        List<JSONObject> prev2List = new ArrayList<>();
//        prev2LedgerList.stream().map(JSONObject.class::cast).forEach(json -> {
//            String code = json.getString("CODE编码");
//            if(codes.contains(code)) {
//                String sCode = json.getString("SCODE样本编码");
//                String personCode = json.getString("PERSON人码");
//                String year = json.getString("YEAR年");
//                String month = json.getString("MONTH月");
//                String money = json.getString("MONEY金额");
//
//                prev2List.stream().filter(json2 -> json2.getString("SCODE样本编码").equalsIgnoreCase(sCode))
//                        .filter(json2 -> json2.getString("PERSON人码").equalsIgnoreCase(personCode))
//                        .findFirst()
//                        .ifPresentOrElse(json2 -> {
//                            json2.put("MONEY金额", new BigDecimal(json2.getString("MONEY金额")).add(new BigDecimal(money)).toString());
//                        }, () -> {
//                            JSONObject newJson = new JSONObject();
//                            newJson.put("SCODE样本编码", sCode);
//                            newJson.put("PERSON人码", personCode);
//                            newJson.put("YEAR年", year);
//                            newJson.put("MONTH月", month);
//                            newJson.put("MONEY金额", money);
//                            prev2List.add(newJson);
//                        });
//            }
//        });
//
//
//        // 上个月家庭户中人员有相关code的记录
//        List<JSONObject> prev1List = new ArrayList<>();
//        prev1LedgerList.stream().map(JSONObject.class::cast).forEach(json -> {
//            String code = json.getString("CODE编码");
//            if(codes.contains(code)) {
//                String sCode = json.getString("SCODE样本编码");
//                String personCode = json.getString("PERSON人码");
//                String year = json.getString("YEAR年");
//                String month = json.getString("MONTH月");
//                String money = json.getString("MONEY金额");
//
//                prev1List.stream().filter(json2 -> json2.getString("SCODE样本编码").equalsIgnoreCase(sCode))
//                        .filter(json2 -> json2.getString("PERSON人码").equalsIgnoreCase(personCode))
//                        .findFirst()
//                        .ifPresentOrElse(json2 -> {
//                            json2.put("MONEY金额", new BigDecimal(json2.getString("MONEY金额")).add(new BigDecimal(money)).toString());
//                        }, () -> {
//                            JSONObject newJson = new JSONObject();
//                            newJson.put("SCODE样本编码", sCode);
//                            newJson.put("PERSON人码", personCode);
//                            newJson.put("YEAR年", year);
//                            newJson.put("MONTH月", month);
//                            newJson.put("MONEY金额", money);
//                            prev1List.add(newJson);
//                        });
//            }
//        });
//
//        // 同时在两个月都存在相关code的记录
//        List<JSONObject> prev2AndPrev1List = prev2List.stream()
//                .filter(json -> !json.getString("PERSON人码").equalsIgnoreCase("99"))
//                .filter(json ->
//                        prev1List.stream().anyMatch(json2 -> json2.getString("SCODE样本编码").equals(json.getString("SCODE样本编码")))
//                                && prev1List.stream().anyMatch(json2 -> json2.getString("PERSON人码").equals(json.getString("PERSON人码")))
//                )
//                .toList();
//
//        // 同时在两个月都存在相关code的记录，但是在本月没有相关code的记录
//        List<JSONObject> prev2AndPrev1ButNoCurrentList = prev2AndPrev1List.stream()
//                .filter(json ->
//                        ledgerList.stream().map(JSONObject.class::cast).noneMatch(json2 ->
//                                json2.getString("SCODE样本编码").equalsIgnoreCase(json.getString("SCODE样本编码"))
//                                        && json2.getString("PERSON人码").equalsIgnoreCase(json.getString("PERSON人码"))
//                                        && codes.contains(json2.getString("CODE编码"))
//                        )
//                )
//                .toList();
//
//        List<TResultItem> items = new ArrayList<>();
//        if(prev2AndPrev1ButNoCurrentList.isEmpty()) {
//            return items;
//        }
//
//        for (JSONObject jsonObject : prev2AndPrev1ButNoCurrentList) {
//            // sCode
//            String sCode = jsonObject.getString("SCODE样本编码");
//            String personCode = jsonObject.getString("PERSON人码");
//
//            // 从memberList中找到户主姓名
//            String sName = memberList.stream().map(JSONObject.class::cast)
//                    .filter(json -> json.getString("SCODE").equalsIgnoreCase(sCode))
//                    .filter(json -> json.getString("A100").equalsIgnoreCase(personCode))
//                    .map(json -> json.getString("A101"))
//                    .findFirst()
//                    .orElse("");
//
//            // 查询上上月和上月的对应的记录
//            JSONObject prev2Json = prev2List.stream()
//                    .filter(json -> json.getString("SCODE样本编码").equalsIgnoreCase(sCode))
//                    .filter(json -> json.getString("PERSON人码").equalsIgnoreCase(personCode))
//                    .findFirst()
//                    .orElse(null);
//            if(prev2Json == null) {
//                continue;
//            }
//
//            JSONObject prev1Json = prev1List.stream()
//                    .filter(json -> json.getString("SCODE样本编码").equalsIgnoreCase(sCode))
//                    .filter(json -> json.getString("PERSON人码").equalsIgnoreCase(personCode))
//                    .findFirst()
//                    .orElse(null);
//            if(prev1Json == null) {
//                continue;
//            }
//
//            String prev2Year = prev2Json.getString("YEAR年");
//            String prev2Month = prev2Json.getString("MONTH月");
//            String prev1Year = prev1Json.getString("YEAR年");
//            String prev1Month = prev1Json.getString("MONTH月");
//            BigDecimal prev2Annuity = new BigDecimal(prev2Json.getString("MONEY金额"));
//            BigDecimal prev1Annuity = new BigDecimal(prev1Json.getString("MONEY金额"));
//
//            TResultItem item = new TResultItem();
//            item.setCreateTime(LocalDateTime.now());
//            item.setSCode(sCode);
//            item.setSPerson(personCode);
//            if(StringUtils.isBlank(sName)) {
//                item.setProcessContent("上上月(" + prev2Year + "年" + prev2Month + "月)" + subject + prev2Annuity + "元，上月(" + prev1Year + "年" + prev1Month + "月)" + subject + prev1Annuity + "元，本月未找到" + subject + "，A卷中未找到对应的人名");
//            } else {
//                item.setSName(sName);
//                item.setProcessContent("上上月(" + prev2Year + "年" + prev2Month + "月)" + subject + prev2Annuity + "元，上月(" + prev1Year + "年" + prev1Month + "月)" + subject + prev1Annuity + "元，本月未找到" + subject);
//            }
//
//            items.add(item);
//        }
//
//        return items;
//    }





    /**
     * 计算上上月和上月都存在相关code的记录，但是在本月没有相关code的记录
     */
    private List<TResultItem> calcOmissionByLastTwoMonths(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList,
            List<String> codes, String subject
    ) {
        List<TResultItem> items = new ArrayList<>();

        // 以A卷为基准，遍历A卷的所有户人员
        memberList.forEach(json -> {
            String sCode = json.getString("SCODE");
            String personCode = json.getString("A100");
            String sName = json.getString("A101");

            if(StringUtils.isBlank(personCode) || StringUtils.isBlank(personCode) || StringUtils.isBlank(sName)) {
                return;
            }

            // 查询上上月、上月和本月对应的记录
            List<JSONObject> prev2Objects = filterLedger(prev2LedgerList, sCode, personCode, codes);
            List<JSONObject> prev1Objects = filterLedger(prev1LedgerList, sCode, personCode, codes);
            List<JSONObject> currentObjects = filterLedger(ledgerList, sCode, personCode, codes);

            if(!prev2Objects.isEmpty() && !prev1Objects.isEmpty() && currentObjects.isEmpty()) {

                String prev2Year = prev2Objects.get(0).getString("YEAR年");
                String prev2Month = prev2Objects.get(0).getString("MONTH月");
                String prev1Year = prev1Objects.get(0).getString("YEAR年");
                String prev1Month = prev1Objects.get(0).getString("MONTH月");

                // 金额求和
                BigDecimal prev2Amount = calcLedgerAmount(prev2Objects);
                BigDecimal prev1Amount = calcLedgerAmount(prev1Objects);

                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setSPerson(personCode);
                item.setSName(sName);
                item.setProcessContent("上上月(" + prev2Year + "年" + prev2Month + "月)" + subject + prev2Amount + "元，上月(" + prev1Year + "年" + prev1Month + "月)" + subject + prev1Amount + "元，本月未找到" + subject);
                items.add(item);
            }
        });

        return items;
    }


    /**
     * 有住院费用支出，无在外饮食
     */
    public List<TResultItem> hospitalizationExpensesButNoOutDiet(List<JSONObject> memberList, List<JSONObject> ledgerList) {

//        Map<String, List<JSONObject>> ledgerSCodeMap = ledgerList.stream().map(JSONObject.class::cast)
//                .collect(
//                        HashMap::new,
//                        (map, json) -> {
//                            String sCode = json.getString("SCODE样本编码");
//                            map.computeIfAbsent(sCode, k -> new ArrayList<>()).add(json);
//                        },
//                        HashMap::putAll
//                );

//        List<TResultItem> items = new ArrayList<>();
//        Map<String, List<JSONObject>> ledgerSCodeMap = groupByColumn(ledgerList, "SCODE样本编码");
//
//        ledgerSCodeMap.forEach((sCode, lines) -> {
//
//            // 住院费用支出合计
//            BigDecimal expense = lines.stream()
//                    .filter(json -> json.getString("CODE编码").equalsIgnoreCase("372211"))
//                    .map(json -> {
//                        String money = json.getString("MONEY金额");
//                        if (StringUtils.isBlank(money)) {
//                            return BigDecimal.ZERO;
//                        }
//
//                        return new BigDecimal(money);
//                    })
//                    .reduce(BigDecimal::add)
//                    .orElse(BigDecimal.ZERO);
//
//            // 在外饮食金额合计
//            BigDecimal diet = lines.stream()
//                    .filter(json -> json.getString("CODE编码").equalsIgnoreCase("313211"))
//                    .map(json -> {
//                        String money = json.getString("MONEY金额");
//                        if (StringUtils.isBlank(money)) {
//                            return BigDecimal.ZERO;
//                        }
//
//                        return new BigDecimal(money);
//                    })
//                    .reduce(BigDecimal::add)
//                    .orElse(BigDecimal.ZERO);
//
//            if(expense.compareTo(BigDecimal.ZERO) > 0 && diet.compareTo(BigDecimal.ZERO) <= 0) {
//                TResultItem item = new TResultItem();
//                item.setCreateTime(LocalDateTime.now());
//                item.setSCode(sCode);
//                item.setProcessContent("有住院费用支出" + expense + "元，无在外饮食");
//                items.add(item);
//            }
//
//        });
//        return items;


        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 住院费用支出记录
            List<JSONObject> expenseObjects = filterLedger(ledgerList, sCode, List.of("372211"));
            // 在外饮食记录
            List<JSONObject> dietObjects = filterLedger(ledgerList, sCode, List.of("313211"));

            // 住院费用支出合计
            BigDecimal expense = calcLedgerAmount(expenseObjects);
            // 在外饮食金额合计
            BigDecimal diet = calcLedgerAmount(dietObjects);

            // 有住院费用支出，无在外饮食
            if(expense.compareTo(BigDecimal.ZERO) > 0 && diet.compareTo(BigDecimal.ZERO) <= 0) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("有住院费用支出" + expense + "元，无在外饮食");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 月度人均食品和在外饮食不足100元
     */
    public List<TResultItem> monthlyPerCapitaFoodAndOutDietLessThan100(List<JSONObject> memberList, List<JSONObject> ledgerList) {
//        List<TResultItem> items = new ArrayList<>();
//
//        Map<String, List<JSONObject>> memberSCodeMap = groupByColumn(memberList, "SCODE");
//        Map<String, List<JSONObject>> ledgerSCodeMap = groupByColumn(ledgerList, "SCODE样本编码");
//
//        ledgerSCodeMap.forEach((sCode, lines) -> {
//            // 食品消费金额合计
//            BigDecimal food = lines.stream()
//                    .filter(json -> json.getString("CODE编码").startsWith("311"))
//                    .map(json -> {
//                        String money = json.getString("MONEY金额");
//                        if (StringUtils.isBlank(money)) {
//                            return BigDecimal.ZERO;
//                        }
//
//                        return new BigDecimal(money);
//                    })
//                    .reduce(BigDecimal::add)
//                    .orElse(BigDecimal.ZERO);
//
//            // 在外饮食金额合计
//            BigDecimal diet = lines.stream()
//                    .filter(json -> json.getString("CODE编码").equalsIgnoreCase("313211"))
//                    .map(json -> {
//                        String money = json.getString("MONEY金额");
//                        if (StringUtils.isBlank(money)) {
//                            return BigDecimal.ZERO;
//                        }
//
//                        return new BigDecimal(money);
//                    })
//                    .reduce(BigDecimal::add)
//                    .orElse(BigDecimal.ZERO);
//
//            // 统计该户总人数
//            long count = memberSCodeMap.get(sCode).stream()
//                    .filter(json -> json.getString("A119").trim().equalsIgnoreCase("1"))
//                    .count();
//
//            if(count > 0) {
//                BigDecimal value = food.add(diet).divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP);
//                if(value.compareTo(BigDecimal.valueOf(100)) < 0) {
//                    TResultItem item = new TResultItem();
//                    item.setCreateTime(LocalDateTime.now());
//                    item.setSCode(sCode);
//                    item.setProcessContent("月度食品支出" + food + "元，在外饮食金额" + diet + "元，该户总人数" + count + "人，本月食品和在外饮食消费人均" + value + "元");
//                    items.add(item);
//                }
//            }
//        });
//
//        return items;

        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 食品消费金额
            List<JSONObject> foodObjects = filterLedger(ledgerList, sCode, code -> code != null && code.startsWith("311"));
            BigDecimal foodMoney = calcLedgerAmount(foodObjects);

            // 在外饮食金额
            List<JSONObject> dietObjects = filterLedger(ledgerList, sCode, List.of("313211"));
            BigDecimal dietMoney = calcLedgerAmount(dietObjects);

            // 统计该户总人数
            long count = countMemberOfFamily(memberList, sCode);

            if(count > 0) {
                BigDecimal value = foodMoney.add(dietMoney).divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP);
                if(value.compareTo(BigDecimal.valueOf(100)) < 0) {
                    TResultItem item = new TResultItem();
                    item.setCreateTime(LocalDateTime.now());
                    item.setSCode(sCode);
                    item.setProcessContent("月度食品支出" + foodMoney + "元，在外饮食金额" + dietMoney + "元，该户总人数" + count + "人，本月食品和在外饮食消费人均" + value + "元");
                    items.add(item);
                }
            }
        });

        return items;
    }

    /**
     * 养老金漏记
     */
    public List<TResultItem> annuityOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("240121"), "养老金"
        );
    }

    /**
     * 退休金漏记
     */
    public List<TResultItem> pensionOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("240111"), "退休金"
        );
    }

    /**
     * 其他养老金漏记
     */
    public List<TResultItem> otherAnnuityOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("240191"), "其他养老金"
        );
    }

    /**
     * 工资漏记
     */
    public List<TResultItem> salaryOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("210111", "210131"), "工资"
        );
    }

    /**
     * 疑似漏记个人缴纳养老保险
     */
    public List<TResultItem> personPayEndowmentInsuranceOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("532111"), "个人缴纳养老保险"
        );
    }

    /**
     * 疑似漏记个人缴纳医疗保险
     */
    public List<TResultItem> personPayMedicalInsuranceOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("532211"), "个人缴纳医疗保险"
        );
    }

    /**
     * 疑似漏记个人缴纳失业保险
     */
    public List<TResultItem> personPayUnemploymentInsuranceOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("532311"), "个人缴纳失业保险"
        );
    }

    /**
     * 疑似漏记个人缴纳住房公积金
     */
    public List<TResultItem> personPayHousingFundOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        return calcOmissionByLastTwoMonths(
                memberList, ledgerList, prev1LedgerList, prev2LedgerList,
                List.of("532411"), "个人缴纳住房公积金"
        );
    }

    /**
     * 疑似漏记电费支出
     */
    public List<TResultItem> electricityExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 电费支出记录
            List<JSONObject> electricityObjects = filterLedger(ledgerList, sCode, List.of("333211"));
            if (electricityObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("本月疑似漏记电费支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 疑似漏记水费支出
     */
    public List<TResultItem> waterExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 上上月水费支出记录
            List<JSONObject> prev2WaterObjects = filterLedger(prev2LedgerList, sCode, List.of("333111"));
            // 上月水费支出记录
            List<JSONObject> prev1WaterObjects = filterLedger(prev1LedgerList, sCode, List.of("333111"));
            // 本月水费支出记录
            List<JSONObject> currentWaterObjects = filterLedger(ledgerList, sCode, List.of("333111"));

            if (prev2WaterObjects.isEmpty() && prev1WaterObjects.isEmpty() && currentWaterObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("该户上上月、上月与本月都没有水费支出记录，疑似漏记水费支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 疑似漏记天然气支出
     */
    public List<TResultItem> naturalGasExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 上上月水费支出记录
            List<JSONObject> prev2WaterObjects = filterLedger(prev2LedgerList, sCode, List.of("333305"));
            // 上月水费支出记录
            List<JSONObject> prev1WaterObjects = filterLedger(prev1LedgerList, sCode, List.of("333305"));
            // 本月水费支出记录
            List<JSONObject> currentWaterObjects = filterLedger(ledgerList, sCode, List.of("333305"));

            if (prev2WaterObjects.isEmpty() && prev1WaterObjects.isEmpty() && currentWaterObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("该户上上月、上月与本月都没有天然气支出记录，疑似漏记天然气支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 疑似漏记汽油支出
     */
    public List<TResultItem> gasolineExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> familyList, List<JSONObject> ledgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 获取家庭汽车数量
            int carCount = filterFamily(familyList, sCode).stream()
                    .map(json -> json.getOrDefault("B201", "0").toString().trim())
                    .map(value -> {
                        try {
                            return Integer.parseInt(value);
                        } catch (Exception e) {
                            return 0;
                        }
                    })
                    .findFirst()
                    .orElse(0);

            // 获取家庭新能源汽车数量
            int newEnergyCarCount = filterFamily(familyList, sCode).stream()
                    .map(json -> json.getOrDefault("B201_1", "0").toString().trim())
                    .map(value -> {
                        try {
                            return Integer.parseInt(value);
                        } catch (Exception e) {
                            return 0;
                        }
                    })
                    .findFirst()
                    .orElse(0);

            // 燃油汽车数量
            int fuelCarCount = carCount - newEnergyCarCount;
            if(fuelCarCount <= 0) {
                return;
            }

            // 本月汽油支出记录
            List<JSONObject> currentGasolineObjects = filterLedger(ledgerList, sCode, List.of("351311"));

            if(currentGasolineObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("该户有燃油汽车" + fuelCarCount + "辆，本月没有汽油支出记录，疑似漏记汽油支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 疑似漏记交通工具充电费支出
     */
    public List<TResultItem> vehicleElectricityExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> familyList, List<JSONObject> ledgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 获取家庭新能源汽车数量
            int newEnergyCarCount = filterFamily(familyList, sCode).stream()
                    .map(json -> json.getOrDefault("B201_1", "0").toString().trim())
                    .map(value -> {
                        try {
                            return Integer.parseInt(value);
                        } catch (Exception e) {
                            return 0;
                        }
                    })
                    .findFirst()
                    .orElse(0);
            if(newEnergyCarCount <= 0) {
                return;
            }

            // 本月新能源汽车充电支出记录
            List<JSONObject> currentElectricityObjects = filterLedger(ledgerList, sCode, List.of("351331"));

            if(currentElectricityObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("该户有新能源汽车" + newEnergyCarCount + "辆，本月没有新能源汽车充电支出记录，疑似漏记新能源汽车充电支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 疑似漏记话费支出
     */
    public List<TResultItem> telephoneExpenditureOmission(
            List<JSONObject> memberList, List<JSONObject> ledgerList
    ) {
        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 话费支出记录
            List<JSONObject> phoneObjects = filterLedger(ledgerList, sCode, List.of("352211"));
            if (phoneObjects.isEmpty()) {
                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("本月疑似漏记话费支出");
                items.add(item);
            }
        });

        return items;
    }

    /**
     * 教育支出编码选错
     */
    public List<TResultItem> educationExpenditureCodeSelectError(
            List<JSONObject> memberList, List<JSONObject> ledgerList,
            List<JSONObject> prev1LedgerList, List<JSONObject> prev2LedgerList
    ) {
        Map<String, String> stageTitleMap = Map.of(
                "1", "未上过学",
                "2", "小学",
                "3", "初中",
                "4", "高中(职高、中专)",
                "5", "大学专科",
                "6", "大学本科",
                "7", "研究生"
        );

        Map<String, List<String>> educationMoneyMap = Map.of(
                "1", List.of("3611"),
                "2", List.of("3612"),
                "3", List.of("3613"),
                "4", List.of("3614", "3615"),
                "5", List.of("3616"),
                "6", List.of("3616"),
                "7", List.of("3616")
        );

        List<TResultItem> items = new ArrayList<>();

        collectByColumn(memberList, "SCODE").forEach(sCode -> {
            // 家庭成员存在的教育阶段
            Set<String> existedStages = filterMember(memberList, sCode).stream()
                    .map(json -> json.getOrDefault("A113", "").toString().trim())
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            // 家庭成员存在的教育阶段对应的code
            Set<String> existedCodes = existedStages.stream()
                    .map(educationMoneyMap::get)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());

            // 获取非当前教育阶段的code
            Set<String> nonExistedCodes = educationMoneyMap.values().stream()
                    .flatMap(Collection::stream)
                    .filter(code -> !existedCodes.contains(code))
                    .collect(Collectors.toSet());

            List<JSONObject> objects = ledgerList.stream()
                    .filter(json -> sCode.equalsIgnoreCase(json.getOrDefault("SCODE样本编码", "").toString().trim()))
                    .filter(json -> {
                        String code = json.getString("CODE编码");
                        if (StringUtils.isBlank(code)) {
                            return false;
                        }

                        return nonExistedCodes.stream().anyMatch(code::startsWith);
                    })
                    .toList();

            BigDecimal amount = calcLedgerAmount(objects);

            if(!objects.isEmpty() && amount.compareTo(BigDecimal.ZERO) > 0) {
                String stageDsr = existedStages.stream()
                        .map(stageTitleMap::get)
                        .collect(Collectors.joining(","));

                String nonExistedCodeDsr = objects.stream()
                        .map(json -> json.getOrDefault("CODE编码", "").toString().trim())
                        .distinct()
                        .collect(Collectors.joining(","));

                TResultItem item = new TResultItem();
                item.setCreateTime(LocalDateTime.now());
                item.setSCode(sCode);
                item.setProcessContent("家庭成员教育阶段包括：" + stageDsr + "，但是有" + amount + "元的非该教育阶段的教育支出[编码为：" + nonExistedCodeDsr + "]，疑似编码选错");
                items.add(item);
            }

//            filterMember(memberList, sCode).forEach(member -> {
//                String personCode = member.getOrDefault("A100", "").toString().trim();
//                String educationStage = member.getOrDefault("A113", "").toString().trim();
//                String name = member.getOrDefault("A101", "").toString().trim();
//                // 人员编码和教育阶段不能为空，且教育阶段必须在枚举中
//                if(StringUtils.isBlank(personCode) || StringUtils.isBlank(educationStage) || !stageTitleMap.containsKey(educationStage)) {
//                    return;
//                }
//
//                // 获取非当前人员教育阶段的code
//                Set<String> codes = educationMoneyMap.values().stream()
//                        .flatMap(Collection::stream)
//                        .filter(code -> !educationMoneyMap.get(educationStage).contains(code))
//                        .collect(Collectors.toSet());
//
//                List<JSONObject> objects = ledgerList.stream()
//                        .filter(json -> sCode.equalsIgnoreCase(json.getOrDefault("SCODE样本编码", "").toString().trim()))
//                        .filter(json -> personCode.equalsIgnoreCase(json.getOrDefault("PERSON人码", "").toString().trim()))
//                        .filter(json -> {
//                            String code = json.getString("CODE编码");
//                            System.out.println(code);
//                            if (StringUtils.isBlank(code)) {
//                                return false;
//                            }
//
//                            return educationMoneyMap.get(educationStage).stream().anyMatch(code::startsWith);
//                        })
//                        .toList();
//
//                System.out.println(objects);
//
//                BigDecimal amount = calcLedgerAmount(objects);
//
//                if(!objects.isEmpty() && amount.compareTo(BigDecimal.ZERO) > 0) {
//                    TResultItem item = new TResultItem();
//                    item.setCreateTime(LocalDateTime.now());
//                    item.setSCode(sCode);
//                    item.setSPerson(personCode);
//                    item.setSName(name);
//                    item.setProcessContent("教育阶段为" + stageTitleMap.get(educationStage) + "，但是有" + amount + "元的非本教育阶段的教育支出，疑似编码选错");
//                    items.add(item);
//                }
//            });
        });

        return items;
    }
}
