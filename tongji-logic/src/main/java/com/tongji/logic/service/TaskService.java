package com.tongji.logic.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.logic.dto.req.task.TaskAddReq;
import com.tongji.logic.dto.req.task.TaskListReq;
import com.tongji.logic.entity.result.TResultItem;
import com.tongji.logic.entity.result.TResultStat;
import com.tongji.logic.entity.task.TSheet;
import com.tongji.logic.entity.task.TTask;
import com.tongji.logic.mapper.result.TResultItemMapper;
import com.tongji.logic.mapper.result.TResultStatMapper;
import com.tongji.logic.mapper.task.TSheetMapper;
import com.tongji.logic.mapper.task.TTaskMapper;
import com.tongji.logic.task.DataTask;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 数据分析任务 服务类
 * </p>
 */
@Service
@Tag(name = "数据分析任务", description = "数据分析任务相关接口")
public class TaskService {

    @Resource
    private DataTask dataTask;

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private TSheetMapper sheetMapper;

    @Resource
    private TResultItemMapper resultItemMapper;

    @Resource
    private TResultStatMapper resultStatMapper;

    /**
     * 添加任务
     *
     * @param taskAddReq 添加任务请求
     * @param loginUser 登录用户
     */
    @Operation(summary = "添加任务", description = "添加数据分析任务")
    @Transactional(rollbackFor = Exception.class)
    public void addTask(TaskAddReq taskAddReq, SysUser loginUser) {
        // 查询当前选择的表单
        TSheet currentSheet = sheetMapper.selectById(taskAddReq.getLedgerSheetId());
        if(currentSheet == null || currentSheet.getDeleteFlag() == 1) {
            throw new ServiceException("账页记录未找到");
        }

        if(currentSheet.getType() != 11) {
            throw new ServiceException("账页记录类型不支持");
        }

        // 查询A卷
        TSheet memberSheet = sheetMapper.selectFirst(q -> q
                .eq(TSheet::getType, 1)
                .eq(TSheet::getDeleteFlag, 0)
        );
        if(memberSheet == null) {
            throw new ServiceException("A卷记录未找到");
        }

        // 查询B卷
        TSheet familySheet = sheetMapper.selectFirst(q -> q
                .eq(TSheet::getType, 2)
                .eq(TSheet::getDeleteFlag, 0)
        );
        if(familySheet == null) {
            throw new ServiceException("B卷记录未找到");
        }

        // 查询上个月的表单
        TSheet prev1Sheet = sheetMapper.selectFirst(q -> q
                .eq(TSheet::getType, 11)
                .eq(TSheet::getDeleteFlag, 0)
                .eq(TSheet::getLedgerDate, currentSheet.getLedgerDate().minusMonths(1))
        );
        if(prev1Sheet == null) {
            throw new ServiceException("上个月账页记录未找到");
        }

        // 查询上上个月的表单
        TSheet prev2Sheet = sheetMapper.selectFirst(q -> q
                .eq(TSheet::getType, 11)
                .eq(TSheet::getDeleteFlag, 0)
                .eq(TSheet::getLedgerDate, currentSheet.getLedgerDate().minusMonths(2))
        );
        if(prev2Sheet == null) {
            throw new ServiceException("上上个月账页记录未找到");
        }

        TTask task = new TTask();
        task.setAuditType(1);
        task.setLedgerDate(currentSheet.getLedgerDate());
        task.setMemberSheetId(memberSheet.getId());
        task.setFamilySheetId(familySheet.getId());
        task.setLedgerSheetId(currentSheet.getId());
        task.setPrev1LedgerSheetId(prev1Sheet.getId());
        task.setPrev2LedgerSheetId(prev2Sheet.getId());
        // 设置任务初始状态为等待中
        task.setProgress(0);
        task.setRemark(taskAddReq.getRemark());
        task.setCreateBy(loginUser.getUsername());
        task.setCreateUserId(loginUser.getUserId());
        task.setUpdateBy(loginUser.getUsername());
        task.setUpdateTime(LocalDateTime.now());
        task.setDeleteFlag(0);

        taskMapper.insert(task);

        dataTask.awake();
    }

    /**
     * 查询任务分页列表
     *
     * @param listReq 查询条件
     * @return 任务列表
     */
    @Operation(summary = "查询任务分页列表", description = "根据条件查询任务分页列表")
    public TablePage<TTask> queryPage(TaskListReq listReq) {
        return PageUtils.paginate(() -> {
            LambdaQueryWrapper<TTask> wrapper = Wrappers.lambdaQuery();

            wrapper.eq(TTask::getDeleteFlag, 0);

            if (StringUtils.isNotEmpty(listReq.getAuditType())) {
                wrapper.eq(TTask::getAuditType, listReq.getAuditType());
            }

            if (listReq.getProgress() != null) {
                wrapper.eq(TTask::getProgress, listReq.getProgress());
            }

            if (listReq.getCreateUserId() != null) {
                wrapper.eq(TTask::getCreateUserId, listReq.getCreateUserId());
            }

            if (StringUtils.isNotEmpty(listReq.getBeginCreateTime())) {
                wrapper.ge(TTask::getCreateTime, listReq.getBeginCreateTime());
            } else if (StringUtils.isNotEmpty(listReq.getEndCreateTime())) {
                wrapper.le(TTask::getCreateTime, listReq.getEndCreateTime());
            }

            // 按创建时间降序排序
            wrapper.orderByDesc(TTask::getCreateTime);

            return taskMapper.selectList(wrapper);
        });
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "获取任务详情", description = "根据任务ID获取任务详情")
    public TTask queryDetail(Long taskId) {
        TTask task = taskMapper.selectById(taskId);
        if (task == null || task.getDeleteFlag() == 1) {
            throw new ServiceException("任务不存在");
        }
        return task;
    }

    /**
     * 删除任务（逻辑删除）
     *
     * @param taskId 任务ID
     * @param loginUser 登录用户
     */
    @Operation(summary = "删除任务", description = "逻辑删除任务及其相关结果数据")
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(Long taskId, SysUser loginUser) {
        // 查询任务记录
        TTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new ServiceException("任务记录不存在");
        }

        // 检查任务是否已删除
        if (task.getDeleteFlag() == 1) {
            throw new ServiceException("任务记录已删除");
        }

        // 检查任务状态，正在处理中的任务不能删除
        if (task.getProgress() == 1) { // 1=处理中
            throw new ServiceException("任务正在处理中，无法删除");
        }

        // 删除相关的结果数据（物理删除）
        // 删除结果条目
        LambdaQueryWrapper<TResultItem> itemWrapper = Wrappers.lambdaQuery();
        itemWrapper.eq(TResultItem::getTaskId, taskId);
        resultItemMapper.delete(itemWrapper);

        // 删除结果统计
        LambdaQueryWrapper<TResultStat> statWrapper = Wrappers.lambdaQuery();
        statWrapper.eq(TResultStat::getTaskId, taskId);
        resultStatMapper.delete(statWrapper);

        // 设置任务删除标志和更新人信息（逻辑删除）
        task.setDeleteFlag(1);
        task.setUpdateBy(loginUser.getUsername());
        task.setUpdateTime(LocalDateTime.now());

        taskMapper.updateById(task);
    }

}
