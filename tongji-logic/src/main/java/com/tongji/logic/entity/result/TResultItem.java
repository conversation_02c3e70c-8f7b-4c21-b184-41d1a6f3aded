package com.tongji.logic.entity.result;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 结果条目表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_result_item")
@Schema(description = "结果条目表")
public class TResultItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "数据分析任务ID")
    private Long taskId;

    @Schema(description = "数据分析指标ID")
    private Long indicatorId;

    @Schema(description = "户号")
    private String sCode;

    @Schema(description = "人码")
    private String sPerson;

    @Schema(description = "人名")
    private String sName;

    @Schema(description = "数据分析过程描述")
    private String processContent;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


}
