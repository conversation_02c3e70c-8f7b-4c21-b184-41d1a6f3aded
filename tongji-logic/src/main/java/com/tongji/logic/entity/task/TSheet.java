package com.tongji.logic.entity.task;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 表单表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_sheet")
@Schema(description = "表单表")
public class TSheet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "工作表类型[1=A卷;2=B卷;11=月度账页]，A卷和B卷只能同时存在一条未删除的记录，月度账页每个月只能同时存在一条未删除的记录")
    private Integer type;

    @Schema(description = "账页日期,type=11时有效")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ledgerDate;

    @Schema(description = "文件路径,文件路径在记录创建时就需要指定,如果修改需要先删除记录再添加新的记录,以保证任务的数据一致性")
    private String filePath;

    @Schema(description = "任务创建人id")
    private Long createUserId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer deleteFlag;


}
