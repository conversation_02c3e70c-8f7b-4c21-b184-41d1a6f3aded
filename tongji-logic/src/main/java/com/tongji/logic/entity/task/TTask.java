package com.tongji.logic.entity.task;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 数据分析任务
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_task")
@Schema(description = "数据分析任务")
public class TTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "审核类型[month=逐月审核]")
    private String auditType;

    @Schema(description = "A卷文件路径")
    private String aPath;

    @Schema(description = "B卷文件路径")
    private String bPath;

    @Schema(description = "账页文件路径")
    private String ledgerPath;

    @Schema(description = "任务处理进度[0=等待中;1=处理中;2=处理成功;9=处理失败]")
    private Integer progress;

    @Schema(description = "处理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processStartTime;

    @Schema(description = "处理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processEndTime;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "任务创建人id")
    private Long createUserId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "备注")
    private String remark;


}
