package com.tongji.logic.entity.task;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serial;

/**
 * <p>
 * 数据分析任务表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_task")
@Schema(description = "数据分析任务表")
public class TTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "审核类型[1=逐月审核]")
    private Integer auditType;

    @Schema(description = "账页日期,audit_type=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ledgerDate;

    @Schema(description = "家庭成员基本信息(A卷)sheet id")
    private Long memberSheetId;

    @Schema(description = "家庭基本情况(B卷)sheet id")
    private Long familySheetId;

    @Schema(description = "账页文件sheet id")
    private Long ledgerSheetId;

    @Schema(description = "前1个账页sheet id,audit_type=1时代表上个月的账页ID")
    private Long prev1LedgerSheetId;

    @Schema(description = "前2个账页sheet id,audit_type=1时代表上上个月的账页ID")
    private Long prev2LedgerSheetId;

    @Schema(description = "任务处理进度[0=等待中;1=处理中;8=处理成功;9=处理失败]")
    private Integer progress;

    @Schema(description = "结果处理开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processStartTime;

    @Schema(description = "结果处理结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime processEndTime;

    @Schema(description = "结果失败原因,progress=3时有效")
    private String failReason;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "任务创建人id")
    private Long createUserId;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "逻辑删除[0=否;1=是]")
    private Integer deleteFlag;


}
