package com.tongji.logic.task;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.tongji.common.config.AppConfig;
import com.tongji.common.exception.ServiceException;
import com.tongji.logic.core.TaskConstants;
import com.tongji.logic.entity.indicator.TIndicator;
import com.tongji.logic.entity.result.TResultItem;
import com.tongji.logic.entity.task.TSheet;
import com.tongji.logic.entity.task.TTask;
import com.tongji.logic.mapper.indicator.TIndicatorMapper;
import com.tongji.logic.mapper.task.TSheetMapper;
import com.tongji.logic.mapper.task.TTaskMapper;
import com.tongji.logic.model.TaskIndicator;
import com.tongji.logic.model.TaskResult;
import com.tongji.logic.service.MonthProcessService;
import com.tongji.logic.service.ResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class DataTask extends Thread {

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private TIndicatorMapper indicatorMapper;

    @Resource
    private TSheetMapper sheetMapper;

    @Resource
    private MonthProcessService monthProcessService;

    @Resource
    private ResultService resultService;

    private final static Object LOCKER = new Object();

    public void awake() {
        synchronized (LOCKER) {
            LOCKER.notifyAll();
        }
    }

    @Override
    public void run() {

        while (!Thread.currentThread().isInterrupted()) {
            log.info("---开始执行任务---");
            List<TTask> tasks = taskMapper.selectList(q -> q
                    .eq(TTask::getDeleteFlag, 0)
                    .in(TTask::getProgress, TaskConstants.PENDING, TaskConstants.PROCESSING)
            );

            if(tasks.isEmpty()) {
                log.info("---没有需要执行的任务，等待5秒---");
                synchronized (LOCKER) {
                    try {
                        LOCKER.wait();
                    } catch (InterruptedException e) {
                        log.warn("被 interrupt 唤醒", e);
                        break;
                    }
                }
            } else {
                log.info("---有{}个任务需要执行---", tasks.size());
                for (TTask task : tasks) {
                    process(task);
                }
            }
        }
    }

    public void process(TTask task) {
        String baseDir = AppConfig.getUploadPath();

        TaskResult result = new TaskResult();
        result.setProcessStartTime(LocalDateTime.now());

        TSheet memberSheet = sheetMapper.selectById(task.getMemberSheetId());
        if(memberSheet == null) {
            result.setStatus(TaskConstants.FAIL);
            result.setProcessEndTime(LocalDateTime.now());
            result.setFailReason("A卷记录未找到");
            resultService.saveResult(task, result);
            return;
        }

        TSheet familySheet = sheetMapper.selectById(task.getFamilySheetId());
        if(familySheet == null) {
            result.setStatus(TaskConstants.FAIL);
            result.setProcessEndTime(LocalDateTime.now());
            result.setFailReason("B卷记录未找到");
            resultService.saveResult(task, result);
            return;
        }


        File memberFile = Path.of(baseDir, memberSheet.getFilePath()).toFile();
        File familyFile = Path.of(baseDir, familySheet.getFilePath()).toFile();

        if(!memberFile.exists()) {
            result.setStatus(TaskConstants.FAIL);
            result.setProcessEndTime(LocalDateTime.now());
            result.setFailReason("A卷文件不存在");
            resultService.saveResult(task, result);
            return;
        }

        if(!familyFile.exists()) {
            result.setStatus(TaskConstants.FAIL);
            result.setProcessEndTime(LocalDateTime.now());
            result.setFailReason("B卷文件不存在");
            resultService.saveResult(task, result);
            return;
        }

        if(Objects.equals(task.getAuditType(), TaskConstants.AUDIT_TYPE_MONTH)) {
            TSheet ledgerSheet = sheetMapper.selectById(task.getLedgerSheetId());
            if(ledgerSheet == null) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("当月账页记录未找到");
                resultService.saveResult(task, result);
                return;
            }

            TSheet prev1LedgerSheet = sheetMapper.selectById(task.getPrev1LedgerSheetId());
            if(prev1LedgerSheet == null) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("上月账页记录未找到");
                resultService.saveResult(task, result);
                return;
            }

            TSheet prev2LedgerSheet = sheetMapper.selectById(task.getPrev2LedgerSheetId());
            if(prev2LedgerSheet == null) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("上上月账页记录未找到");
                resultService.saveResult(task, result);
                return;
            }

            File ledgerFile = Path.of(baseDir, ledgerSheet.getFilePath()).toFile();

            if(!ledgerFile.exists()) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("账页文件不存在");
                resultService.saveResult(task, result);
                return;
            }

            File prev1LedgerFile = Path.of(baseDir, prev1LedgerSheet.getFilePath()).toFile();
            if(!prev1LedgerFile.exists()) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("上月账页文件不存在");
                resultService.saveResult(task, result);
                return;
            }

            File prev2LedgerFile = Path.of(baseDir, prev2LedgerSheet.getFilePath()).toFile();
            if(!prev2LedgerFile.exists()) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason("上上月账页文件不存在");
                resultService.saveResult(task, result);
                return;
            }

            try {
                result.setIndicators(processMonth(memberFile, familyFile, ledgerFile, prev1LedgerFile, prev2LedgerFile));
                result.setStatus(TaskConstants.SUCCESS);
                result.setProcessEndTime(LocalDateTime.now());
            } catch (Exception e) {
                result.setStatus(TaskConstants.FAIL);
                result.setProcessEndTime(LocalDateTime.now());
                result.setFailReason(e.getMessage());
            }
        } else {
            result.setStatus(TaskConstants.FAIL);
            result.setProcessEndTime(LocalDateTime.now());
            result.setFailReason("不支持的审核类型");
        }

        resultService.saveResult(task, result);
    }

    // 处理逐月审核的任务，先查询有哪些指标，然后根据指标的标识，调用对应的处理方法
    public List<TaskIndicator> processMonth(
            File memberFile, File familyFile,
            File ledgerFile, File prev1LedgerFile, File prev2LedgerFile
    ) {
        JSONArray memberArray, familyArray, ledgerArray;
        try {
            memberArray = readCSV(memberFile, 1);
        } catch (Exception e) {
            throw new ServiceException("A卷文件内容读取失败：" + e.getMessage());
        }

        if(memberArray.isEmpty()) {
            throw new ServiceException("A卷文件内容为空");
        }

        try {
            familyArray = readCSV(familyFile, 1);
        } catch (Exception e) {
            throw new ServiceException("B卷文件内容读取失败：" + e.getMessage());
        }

        if(familyArray.isEmpty()) {
            throw new ServiceException("B卷文件内容为空");
        }

        try {
            ledgerArray = readCSV(ledgerFile, 2);
        } catch (Exception e) {
            throw new ServiceException("账页文件内容读取失败：" + e.getMessage());
        }

        if(ledgerArray.isEmpty()) {
            throw new ServiceException("账页文件内容为空");
        }

        List<TIndicator> indicators = indicatorMapper.selectList(q -> q
                .eq(TIndicator::getAuditType, TaskConstants.AUDIT_TYPE_MONTH)
                .eq(TIndicator::getDeleteFlag, 0)
        );

        if (indicators.isEmpty()) {
            throw new ServiceException("未配置逐月审核指标");
        }

        List<TaskIndicator> taskIndicators = new ArrayList<>();

        for (TIndicator indicator : indicators) {

            TaskIndicator taskIndicator = new TaskIndicator();
            BeanUtils.copyProperties(indicator, taskIndicator);

            List<TResultItem> items = new ArrayList<>();
            if(indicator.getSymbol().equalsIgnoreCase("hospitalization-expenses-but-no-out-diet")) {
                // 计算符合该指标的所有结果记录
                items = monthProcessService.hospitalizationExpensesButNoOutDiet(memberArray, ledgerArray);
            } else if (indicator.getSymbol().equalsIgnoreCase("monthly-per-capita-food-and-out-diet-less-than-100")) {
                // 计算符合该指标的所有结果记录
                items = monthProcessService.monthlyPerCapitaFoodAndOutDietLessThan100(memberArray, ledgerArray);
            }
            for (TResultItem item : items) {
                item.setIndicatorId(indicator.getId());
            }
            taskIndicator.setItems(items);

            taskIndicators.add(taskIndicator);
        }

        return taskIndicators;
    }

    private JSONArray readCSV(File file, Integer contentStartIndex) {
        JSONArray array = new JSONArray();

        List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
        if(lines.isEmpty() || lines.size() == 1) {
            return array;
        }

        String headLine = lines.get(0);
        String[] heads = headLine.split(",", -1);
        for(int i = contentStartIndex; i < lines.size(); i++) {
            String line = lines.get(i);
            String[] values = line.split(",", -1);
            if(heads.length != values.length) {
                throw new ServiceException("表头数量与内容字段数量不一致");
            }

            JSONObject object = new JSONObject();
            for (int j = 0; j < heads.length; j++) {
                object.put(heads[j], values[j]);
            }

            array.add(object);
        }

        return array;
    }


}
