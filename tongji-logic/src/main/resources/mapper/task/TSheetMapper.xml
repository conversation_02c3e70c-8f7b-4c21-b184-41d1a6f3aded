<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tongji.logic.mapper.task.TSheetMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tongji.logic.entity.task.TSheet">
        <id property="id" column="id" />
        <result property="type" column="type" />
        <result property="ledgerDate" column="ledger_date" />
        <result property="filePath" column="file_path" />
        <result property="createUserId" column="create_user_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="deleteFlag" column="delete_flag" />
    </resultMap>

</mapper>
