<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tongji.logic.mapper.task.TTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tongji.logic.entity.task.TTask">
        <id property="id" column="id" />
        <result property="auditType" column="audit_type" />
        <result property="ledgerDate" column="ledger_date" />
        <result property="memberSheetId" column="member_sheet_id" />
        <result property="familySheetId" column="family_sheet_id" />
        <result property="ledgerSheetId" column="ledger_sheet_id" />
        <result property="prev1LedgerSheetId" column="prev1_ledger_sheet_id" />
        <result property="prev2LedgerSheetId" column="prev2_ledger_sheet_id" />
        <result property="progress" column="progress" />
        <result property="processStartTime" column="process_start_time" />
        <result property="processEndTime" column="process_end_time" />
        <result property="failReason" column="fail_reason" />
        <result property="remark" column="remark" />
        <result property="createUserId" column="create_user_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="deleteFlag" column="delete_flag" />
    </resultMap>

</mapper>
