<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tongji.logic.mapper.task.TTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tongji.logic.entity.task.TTask">
        <id property="id" column="id" />
        <result property="auditType" column="audit_type" />
        <result property="aPath" column="a_path" />
        <result property="bPath" column="b_path" />
        <result property="ledgerPath" column="ledger_path" />
        <result property="progress" column="progress" />
        <result property="processStartTime" column="process_start_time" />
        <result property="processEndTime" column="process_end_time" />
        <result property="failReason" column="fail_reason" />
        <result property="createUserId" column="create_user_id" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

</mapper>
