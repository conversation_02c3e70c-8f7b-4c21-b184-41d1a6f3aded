package com.tongji.system.service;

import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.text.Convert;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.StringUtils;
import com.tongji.common.utils.spring.SpringUtils;
import com.tongji.system.entity.SysDept;
import com.tongji.system.entity.SysRole;
import com.tongji.system.entity.SysUser;
import com.tongji.system.domain.TreeSelect;
import com.tongji.system.dto.req.dept.SysDeptAddReq;
import com.tongji.system.dto.req.dept.SysDeptEditReq;
import com.tongji.system.dto.req.dept.SysDeptListReq;
import com.tongji.system.dto.resp.dept.DeptTreeCheckedResp;
import com.tongji.system.mapper.SysDeptMapper;
import com.tongji.system.mapper.SysRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 */
@Service
public class SysDeptService {

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    /**
     * 查询部门管理数据
     * @return 部门信息集合
     */
    public List<SysDept> listDepts(SysDeptListReq listReq) {
        SysDept dept = new SysDept();
        BeanUtils.copyProperties(listReq, dept);
        return sysDeptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门树结构信息
     * @return 部门树信息集合
     */
    public List<TreeSelect> listDeptTree(SysDeptListReq listReq) {
        return buildDeptTreeSelect(listDepts(listReq));
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).toList();
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Long> listDeptIdsByRoleId(Long roleId) {
        SysRole role = sysRoleMapper.selectRoleById(roleId);
        return sysDeptMapper.selectDeptIdsByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept queryDeptById(Long deptId) {
        return sysDeptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int queryNormalChildrenDeptCountById(Long deptId) {
        return sysDeptMapper.selectNormalChildrenDeptCountById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId) {
        int result = sysDeptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId) {
        int result = sysDeptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     * @return 结果
     */
    public boolean checkDeptNameUnique(Long parentId, Long deptId, String deptName) {
        SysDept info = sysDeptMapper.checkDeptNameUnique(deptName, parentId);
        if (StringUtils.isNotNull(info) && info.getDeptId() != (StringUtils.isNull(deptId) ? -1L : deptId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    public void checkDeptDataScope(Long deptId, SysUser loginSysUser) {
        if (!SysUser.isAdmin(loginSysUser.getUserId()) && StringUtils.isNotNull(deptId)) {
            SysDeptListReq listReq = new SysDeptListReq();
            listReq.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).listDepts(listReq);
            if (depts.isEmpty()) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     * @return 结果
     */
    public int addDept(SysDeptAddReq addReq, SysUser loginUser) {
        if (!checkDeptNameUnique(addReq.getParentId(), null, addReq.getDeptName())) {
            throw new ServiceException("新增部门'" + addReq.getDeptName() + "'失败，部门名称已存在");
        }

        SysDept dept = new SysDept();
        BeanUtils.copyProperties(addReq, dept);
        dept.setCreateBy(loginUser.getUsername());

        if(addReq.getParentId() == null || addReq.getParentId() == 0) { // 新增根节点
            dept.setParentId(0L);
            dept.setAncestors("0");
        } else {
            SysDept info = sysDeptMapper.selectDeptById(dept.getParentId());
            // 如果父节点不为正常状态,则不允许新增子节点
            if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
                throw new ServiceException("部门停用，不允许新增");
            }
            dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        }

        return sysDeptMapper.insertDept(dept);
    }

    /**
     * 修改保存部门信息
     * @return 结果
     */
    public int updateDept(SysDeptEditReq editReq, SysUser loginUser) {
        checkDeptDataScope(editReq.getDeptId(), loginUser);
        if (!checkDeptNameUnique(editReq.getParentId(), editReq.getDeptId(), editReq.getDeptName())) {
            throw new ServiceException("修改部门'" + editReq.getDeptName() + "'失败，部门名称已存在");
        } else if (editReq.getParentId().equals(editReq.getDeptId())) {
            throw new ServiceException("修改部门'" + editReq.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, editReq.getStatus()) && queryNormalChildrenDeptCountById(editReq.getDeptId()) > 0) {
            throw new ServiceException("该部门包含未停用的子部门！");
        }

        SysDept dept = new SysDept();
        BeanUtils.copyProperties(editReq, dept);
        dept.setUpdateBy(loginUser.getUsername());

        SysDept newParentDept = sysDeptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = sysDeptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = sysDeptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        List<Long> deptIds = Convert.toLongList(ancestors);
        sysDeptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = sysDeptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (!children.isEmpty()) {
            sysDeptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId) {
        return sysDeptMapper.deleteDeptById(deptId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = listChildren(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> listChildren(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<>();
        for (SysDept n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return !listChildren(list, t).isEmpty();
    }

    /**
     * 查询部门树和选中的部门
     *
     * @param roleId 角色ID
     * @return DeptTreeCheckedResp 包含部门树和选中的部门ID列表
     */
    public DeptTreeCheckedResp queryDeptTreeAndCheckedKeys(Long roleId) {
        List<TreeSelect> depts = this.listDeptTree(new SysDeptListReq());
        List<Long> checkedKeys = this.listDeptIdsByRoleId(roleId);
        return new DeptTreeCheckedResp(depts, checkedKeys);
    }
}
