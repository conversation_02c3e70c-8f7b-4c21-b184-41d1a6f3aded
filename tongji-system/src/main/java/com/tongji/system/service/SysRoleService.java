package com.tongji.system.service;

import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.common.utils.spring.SpringUtils;
import com.tongji.system.entity.*;
import com.tongji.system.dto.req.role.*;
import com.tongji.system.mapper.SysRoleDeptMapper;
import com.tongji.system.mapper.SysRoleMapper;
import com.tongji.system.mapper.SysRoleMenuMapper;
import com.tongji.system.mapper.SysUserRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 角色 业务层处理
 */
@Service
public class SysRoleService {

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysRoleDeptMapper sysRoleDeptMapper;

    public List<SysRole> selectRoleList(SysRoleListReq listReq) {
        return sysRoleMapper.selectRoleList(listReq);
    }

    /**
     * 根据条件分页查询角色数据
     *
     * @param listReq 角色信息
     * @return 角色数据集合信息
     */
    public TablePage<SysRole> selectRoleListPage(SysRoleListReq listReq) {
        return PageUtils.paginate(() -> sysRoleMapper.selectRoleList(listReq));
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<SysRole> selectRolesByUserId(Long userId) {
        List<SysRole> userRoles = sysRoleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleAll();
        for (SysRole role : roles) {
            for (SysRole userRole : userRoles) {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue()) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<SysRole> perms = sysRoleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    public List<SysRole> selectRoleAll() {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRoleListReq());
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    public List<Long> selectRoleListByUserId(Long userId) {
        return sysRoleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    public SysRole selectRoleById(Long roleId) {
        return sysRoleMapper.selectRoleById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @return 结果
     */
    public boolean checkRoleNameUnique(Long roleId, String roleName) {
        long id = StringUtils.isNull(roleId) ? -1L : roleId;
        SysRole info = sysRoleMapper.checkRoleNameUnique(roleName);
        if (StringUtils.isNotNull(info) && info.getRoleId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @return 结果
     */
    public boolean checkRoleKeyUnique(Long roleId, String roleKey) {
        long id = StringUtils.isNull(roleId) ? -1L : roleId;
        SysRole info = sysRoleMapper.checkRoleKeyUnique(roleKey);
        if (StringUtils.isNotNull(info) && info.getRoleId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     */
    public void checkRoleAllowed(Long roleId) {
        if (StringUtils.isNotNull(roleId) && SysRole.isAdmin(roleId)) {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleIds 角色id
     */
    public void checkRoleDataScope(List<Long> roleIds, SysUser sysUser) {
        if (!sysUser.isAdmin()) {
            for (Long roleId : roleIds) {
                SysRoleListReq listReq = new SysRoleListReq();
                listReq.setRoleId(roleId);
                List<SysRole> roles = SpringUtils.getAopProxy(this).selectRoleList(listReq);
                if (StringUtils.isEmpty(roles)) {
                    throw new ServiceException("没有权限访问角色数据！");
                }
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    public int countUserRoleByRoleId(Long roleId) {
        return sysUserRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     * @return 结果
     */
    @Transactional
    public int insertRole(SysRoleAddReq req, SysUser sysUser) {
        if (!checkRoleNameUnique(null, req.getRoleName())) {
            throw new ServiceException("新增角色'" + req.getRoleName() + "'失败，角色名称已存在");
        } else if (!checkRoleKeyUnique(null, req.getRoleKey())) {
            throw new ServiceException("新增角色'" + req.getRoleName() + "'失败，角色权限已存在");
        }

        // 新增角色信息
        SysRole role = new SysRole();
        BeanUtils.copyProperties(req, role);
        role.setCreateBy(sysUser.getUsername());

        sysRoleMapper.insertRole(role);
        return insertRoleMenu(role);
    }

    /**
     * 修改保存角色信息
     * @return 结果
     */
    @Transactional
    public int updateRole(SysRoleEditReq req, SysUser sysUser) {
        checkRoleAllowed(req.getRoleId());
        checkRoleDataScope(List.of(req.getRoleId()), sysUser);
        if (!checkRoleNameUnique(req.getRoleId(), req.getRoleName())) {
            throw new ServiceException("修改角色'" + req.getRoleName() + "'失败，角色名称已存在");
        } else if (!checkRoleKeyUnique(req.getRoleId(), req.getRoleKey())) {
            throw new ServiceException("修改角色'" + req.getRoleName() + "'失败，角色权限已存在");
        }

        // 修改角色信息
        SysRole role = new SysRole();
        BeanUtils.copyProperties(req, role);
        role.setUpdateBy(sysUser.getUsername());

        sysRoleMapper.updateRole(role);
        // 删除角色与菜单关联
        sysRoleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        return insertRoleMenu(role);
    }

    /**
     * 修改角色状态
     * @return 结果
     */
    public int updateRoleStatus(SysRoleStatusChangeReq req, SysUser sysUser) {
        checkRoleAllowed(req.getRoleId());
        checkRoleDataScope(List.of(req.getRoleId()), sysUser);

        SysRole role = new SysRole();
        role.setRoleId(req.getRoleId());
        role.setStatus(req.getStatus());
        
        return sysRoleMapper.updateRole(role);
    }

    /**
     * 修改数据权限信息
     * @return 结果
     */
    @Transactional
    public void authDataScope(SysRoleDataScopeReq req, SysUser sysUser) {
        checkRoleAllowed(req.getRoleId());
        checkRoleDataScope(List.of(req.getRoleId()), sysUser);

        // 修改角色信息
        SysRole role = new SysRole();
        BeanUtils.copyProperties(req, role);
        role.setUpdateBy(sysUser.getUsername());

        sysRoleMapper.updateRole(role);
        // 删除角色与部门关联
        sysRoleDeptMapper.deleteRoleDeptByRoleId(role.getRoleId());
        // 新增角色和部门信息（数据权限）
        insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (!list.isEmpty()) {
            rows = sysRoleMenuMapper.batchRoleMenu(list);
        }
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(SysRole role) {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<SysRoleDept> list = new ArrayList<>();
        for (Long deptId : role.getDeptIds()) {
            SysRoleDept rd = new SysRoleDept();
            rd.setRoleId(role.getRoleId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        if (!list.isEmpty()) {
            rows = sysRoleDeptMapper.batchRoleDept(list);
        }
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Transactional
    public int deleteRoleById(Long roleId) {
        // 删除角色与菜单关联
        sysRoleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与部门关联
        sysRoleDeptMapper.deleteRoleDeptByRoleId(roleId);
        return sysRoleMapper.deleteRoleById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Transactional
    public int deleteRoleByIds(List<Long> roleIds, SysUser loginSysUser) {
        for (Long roleId : roleIds) {
            checkRoleAllowed(roleId);
            checkRoleDataScope(List.of(roleId), loginSysUser);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        sysRoleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与部门关联
        sysRoleDeptMapper.deleteRoleDept(roleIds);
        return sysRoleMapper.deleteRoleByIds(roleIds);
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    public int deleteAuthUser(SysUserRole userRole) {
        return sysUserRoleMapper.deleteUserRoleInfo(userRole);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    public int deleteAuthUsers(Long roleId, List<Long> userIds) {
        return sysUserRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    public int insertAuthUsers(Long roleId, List<Long> userIds) {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<>();
        for (Long userId : userIds) {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return sysUserRoleMapper.batchUserRole(list);
    }
}
