package com.tongji.system.service;

import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.PageUtils;
import com.tongji.system.entity.SysDictData;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.dict.SysDictDataAddReq;
import com.tongji.system.dto.req.dict.SysDictDataEditReq;
import com.tongji.system.dto.req.dict.SysDictDataListReq;
import com.tongji.system.mapper.SysDictDataMapper;
import com.tongji.system.utils.DictUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典 业务层处理
 */
@Service
public class SysDictDataService {

    @Resource
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 根据条件分页查询字典数据
     * @return 字典数据集合信息
     */
    public TablePage<SysDictData> selectDictDataListPage(SysDictDataListReq listReq) {
        return PageUtils.paginate(() -> sysDictDataMapper.selectDictDataList(listReq));
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    public String selectDictLabel(String dictType, String dictValue) {
        return sysDictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    public SysDictData selectDictDataById(Long dictCode) {
        return sysDictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    public void deleteDictDataByIds(List<Long> dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            sysDictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     * @return 结果
     */
    public int insertDictData(SysDictDataAddReq addReq, SysUser loginUser) {
        SysDictData data = new SysDictData();
        BeanUtils.copyProperties(addReq, data);
        data.setCreateBy(loginUser.getUsername());

        int row = sysDictDataMapper.insertDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 修改保存字典数据信息
     * @return 结果
     */
    public int updateDictData(SysDictDataEditReq editReq, SysUser loginUser) {
        SysDictData data = new SysDictData();
        BeanUtils.copyProperties(editReq, data);
        data.setUpdateBy(loginUser.getUsername());
        
        int row = sysDictDataMapper.updateDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }
}
