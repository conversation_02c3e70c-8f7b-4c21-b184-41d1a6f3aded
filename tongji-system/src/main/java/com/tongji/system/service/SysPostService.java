package com.tongji.system.service;

import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysPost;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.post.SysPostAddReq;
import com.tongji.system.dto.req.post.SysPostEditReq;
import com.tongji.system.dto.req.post.SysPostListReq;
import com.tongji.system.mapper.SysPostMapper;
import com.tongji.system.mapper.SysUserPostMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 岗位信息 服务层处理
 */
@Service
public class SysPostService {

    @Resource
    private SysPostMapper sysPostMapper;

    @Resource
    private SysUserPostMapper sysUserPostMapper;

    /**
     * 查询岗位信息集合
     * @return 岗位信息集合
     */
    public TablePage<SysPost> selectPostList(SysPostListReq listReq) {
        return PageUtils.paginate(() -> sysPostMapper.selectPostList(listReq));
    }

    /**
     * 查询所有岗位
     *
     * @return 岗位列表
     */
    public List<SysPost> selectPostAll() {
        return sysPostMapper.selectPostAll();
    }

    /**
     * 通过岗位ID查询岗位信息
     *
     * @param postId 岗位ID
     * @return 角色对象信息
     */
    public SysPost selectPostById(Long postId) {
        return sysPostMapper.selectPostById(postId);
    }

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    public List<Long> selectPostListByUserId(Long userId) {
        return sysPostMapper.selectPostListByUserId(userId);
    }

    /**
     * 校验岗位名称是否唯一
     *
     * @param postId 岗位Id
     * @return 结果
     */
    public boolean checkPostNameUnique(Long postId, String postName) {
        long id = StringUtils.isNull(postId) ? -1L : postId;
        SysPost info = sysPostMapper.checkPostNameUnique(postName);
        if (StringUtils.isNotNull(info) && info.getPostId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码是否唯一
     * @return 结果
     */
    public boolean checkPostCodeUnique(Long postId, String postCode) {
        long id = StringUtils.isNull(postId) ? -1L : postId;
        SysPost info = sysPostMapper.checkPostCodeUnique(postCode);
        if (StringUtils.isNotNull(info) && info.getPostId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int countUserPostById(Long postId) {
        return sysUserPostMapper.countUserPostById(postId);
    }

    /**
     * 删除岗位信息
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int deletePostById(Long postId) {
        return sysPostMapper.deletePostById(postId);
    }

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位ID
     * @return 结果
     */
    public int deletePostByIds(List<Long> postIds) {
        for (Long postId : postIds) {
            SysPost post = selectPostById(postId);
            if (countUserPostById(postId) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", post.getPostName()));
            }
        }
        return sysPostMapper.deletePostByIds(postIds);
    }

    /**
     * 新增保存岗位信息
     * @return 结果
     */
    public int insertPost(SysPostAddReq postAddReq, SysUser loginUser) {
        SysPost post = new SysPost();
        BeanUtils.copyProperties(postAddReq, post);
        if (!checkPostNameUnique(null, post.getPostName())) {
            throw new ServiceException("新增岗位'" + post.getPostName() + "'失败，岗位名称已存在");
        } else if (!checkPostCodeUnique(null, post.getPostCode())) {
            throw new ServiceException("新增岗位'" + post.getPostName() + "'失败，岗位编码已存在");
        }
        post.setCreateBy(loginUser.getUsername());
        return sysPostMapper.insertPost(post);
    }

    /**
     * 修改保存岗位信息
     * @return 结果
     */
    public int updatePost(SysPostEditReq postEditReq, SysUser loginUser) {
        if (!checkPostNameUnique(postEditReq.getPostId(), postEditReq.getPostName())) {
            throw new ServiceException("修改岗位'" + postEditReq.getPostName() + "'失败，岗位名称已存在");
        } else if (!checkPostCodeUnique(postEditReq.getPostId(), postEditReq.getPostCode())) {
            throw new ServiceException("修改岗位'" + postEditReq.getPostName() + "'失败，岗位编码已存在");
        }
        SysPost post = new SysPost();
        BeanUtils.copyProperties(postEditReq, post);
        post.setUpdateBy(loginUser.getUsername());
        return sysPostMapper.updatePost(post);
    }
}
