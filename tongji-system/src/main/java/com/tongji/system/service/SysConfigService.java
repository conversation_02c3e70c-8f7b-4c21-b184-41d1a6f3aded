package com.tongji.system.service;

import com.tongji.common.annotation.DataSource;
import com.tongji.common.constant.CacheConstants;
import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.core.redis.RedisCache;
import com.tongji.common.core.text.Convert;
import com.tongji.common.enums.DataSourceType;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysConfig;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.config.SysConfigAddReq;
import com.tongji.system.dto.req.config.SysConfigEditReq;
import com.tongji.system.dto.req.config.SysConfigListReq;
import com.tongji.system.mapper.SysConfigMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 参数配置 服务层实现
 */
@Service
public class SysConfigService {

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private RedisCache redisCache;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @DataSource(DataSourceType.MASTER)
    public SysConfig queryConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return sysConfigMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    public String queryConfigByKey(String configKey) {
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = sysConfigMapper.selectConfig(config);
        if (StringUtils.isNotNull(retConfig)) {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    public boolean queryCaptchaEnabled() {
        String captchaEnabled = queryConfigByKey("sys.account.captchaEnabled");
        if (StringUtils.isEmpty(captchaEnabled)) {
            return true;
        }
        return Convert.toBool(captchaEnabled);
    }

    /**
     * 查询参数配置列表
     *
     * @param listReq 参数配置信息
     * @return 参数配置集合
     */
    public List<SysConfig> listConfig(SysConfigListReq listReq) {
        return sysConfigMapper.selectConfigList(listReq);
    }


    /**
     * 查询参数配置列表
     *
     * @param listReq 参数配置信息
     * @return 参数配置集合
     */
    public TablePage<SysConfig> listConfigPage(SysConfigListReq listReq) {
        return PageUtils.paginate(() -> sysConfigMapper.selectConfigList(listReq));
    }

    /**
     * 新增参数配置
     *
     * @param addReq 参数配置信息
     * @return 结果
     */
    public int addConfig(SysConfigAddReq addReq, SysUser loginUser) {
        if (!checkConfigKeyUnique(null, addReq.getConfigKey())) {
            throw new ServiceException("新增参数'" + addReq.getConfigName() + "'失败，参数键名已存在");
        }

        SysConfig config = new SysConfig();
        BeanUtils.copyProperties(addReq, config);
        config.setCreateBy(loginUser.getUsername());

        int row = sysConfigMapper.insertConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     *
     * @param editReq 参数配置信息
     * @return 结果
     */
    public int updateConfig(SysConfigEditReq editReq, SysUser loginUser) {
        if (!checkConfigKeyUnique(editReq.getConfigId(), editReq.getConfigKey())) {
            throw new ServiceException("修改参数'" + editReq.getConfigName() + "'失败，参数键名已存在");
        }
        SysConfig config = new SysConfig();
        BeanUtils.copyProperties(editReq, config);
        config.setUpdateBy(loginUser.getUsername());

        SysConfig temp = sysConfigMapper.selectConfigById(config.getConfigId());
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey())) {
            redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
        }

        int row = sysConfigMapper.updateConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    public void deleteConfigByIds(List<Long> configIds) {
        for (Long configId : configIds) {
            SysConfig config = queryConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            sysConfigMapper.deleteConfigById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    public void loadingConfigCache() {
        List<SysConfig> configsList = sysConfigMapper.selectConfigList(new SysConfigListReq());
        for (SysConfig config : configsList) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @return 结果
     */
    public boolean checkConfigKeyUnique(Long configId, String configKey) {
        SysConfig info = sysConfigMapper.checkConfigKeyUnique(configKey);
        if (StringUtils.isNotNull(info) && info.getConfigId() != (StringUtils.isNull(configId) ? -1L : configId)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }
}
