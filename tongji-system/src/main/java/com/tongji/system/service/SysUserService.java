package com.tongji.system.service;

import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.*;
import com.tongji.system.dto.req.user.*;
import com.tongji.system.dto.resp.user.UserAuthRoleResp;
import com.tongji.system.dto.resp.user.UserInfoResp;
import com.tongji.system.mapper.*;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 */
@Service
public class SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysPostMapper sysPostMapper;

    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Resource
    private SysUserPostMapper sysUserPostMapper;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysPostService sysPostService;

    /**
     * 根据条件分页查询用户列表
     * @return 用户信息集合信息
     */
    public TablePage<SysUser> selectUserListPage(SysUserListReq listReq) {
        return PageUtils.paginate(
                () -> sysUserMapper.selectUserList(listReq),
                (sysUser) -> {
                    sysUser.setRoleIds(sysUser.getRoles().stream().map(SysRole::getRoleId).toList());
                    return sysUser;
                }
        );
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     * @param req 查询请求对象
     * @return 用户信息集合信息
     */
    public TablePage<SysUser> selectAllocatedList(SysUserAllocatedRoleQueryReq req) {
        return PageUtils.paginate(() -> sysUserMapper.selectAllocatedList(req.getRoleId(), req.getUsername(), req.getPhoneNumber()));
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param req 查询请求对象
     * @return 用户信息集合信息
     */
    public TablePage<SysUser> selectUnallocatedList(SysUserAllocatedRoleQueryReq req) {
        return PageUtils.paginate(() -> sysUserMapper.selectUnallocatedList(req.getRoleId(), req.getUsername(), req.getPhoneNumber()));
    }

    /**
     * 通过用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUsername(String username) {
        return sysUserMapper.selectUserByUsername(username);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public UserInfoResp selectUserById(Long userId, SysUser loginUser) {
        checkUserDataScope(userId, loginUser);

        UserInfoResp userInfoResp = new UserInfoResp();
        List<SysRole> roles = sysRoleService.selectRoleAll();
        userInfoResp.setRoles(SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        userInfoResp.setPosts(sysPostService.selectPostAll());

        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = sysUserMapper.selectUserById(userId);
            userInfoResp.setPostIds(sysPostService.selectPostListByUserId(userId));
            userInfoResp.setRoleIds(sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
            sysUser.setRoleIds(userInfoResp.getRoleIds());
            sysUser.setPostIds(userInfoResp.getPostIds());
            userInfoResp.setUser(sysUser);
        }

        return userInfoResp;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userId 用户Id
     * @return 结果
     */
    public String selectUserRoleGroup(Long userId) {
        List<SysRole> list = sysRoleMapper.selectRolesByUsername(userId);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }

        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userId 用户Id
     * @return 结果
     */
    public String selectUserPostGroup(Long userId) {
        List<SysPost> list = sysPostMapper.selectPostsByUsername(userId);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     * @return 结果
     */
    public boolean checkUsernameUnique(Long userId, String username) {
        long id = userId == null ? -1L : userId;
        SysUser info = sysUserMapper.checkUsernameUnique(username);
        if (StringUtils.isNotNull(info) && info.getUserId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     */
    public boolean checkPhoneUnique(Long userId, String phoneNumber) {
        long id = userId == null ? -1L : userId;
        SysUser info = sysUserMapper.checkPhoneUnique(phoneNumber);
        if (StringUtils.isNotNull(info) && info.getUserId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     */
    public boolean checkEmailUnique(Long userId, String email) {
        long id = userId == null ? -1L : userId;
        SysUser info = sysUserMapper.checkEmailUnique(email);
        if (StringUtils.isNotNull(info) && info.getUserId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     */
    public void checkUserAllowed(Long userId) {
        if (SysUser.isAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId, SysUser loginSysUser) {
        if (!loginSysUser.isAdmin()) {
            SysUserListReq listReq = new SysUserListReq();
            listReq.setUserId(userId);
            List<SysUser> users = sysUserMapper.selectUserList(listReq);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     * @return 结果
     */
    @Transactional
    public int insertUser(SysUserAddReq userAddReq, SysUser loginUser) {
        sysDeptService.checkDeptDataScope(userAddReq.getDeptId(), loginUser);
        sysRoleService.checkRoleDataScope(userAddReq.getRoleIds(), loginUser);
        
        if (!checkUsernameUnique(null, userAddReq.getUsername())) {
            throw new ServiceException("新增用户'" + userAddReq.getUsername() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(userAddReq.getPhoneNumber()) && !checkPhoneUnique(null, userAddReq.getPhoneNumber())) {
            throw new ServiceException("新增用户'" + userAddReq.getUsername() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(userAddReq.getEmail()) && !checkEmailUnique(null, userAddReq.getEmail())) {
            throw new ServiceException("新增用户'" + userAddReq.getUsername() + "'失败，邮箱账号已存在");
        }

        SysUser user = new SysUser();
        BeanUtils.copyProperties(userAddReq, user);
        user.setCreateBy(loginUser.getUsername());
        
        // 新增用户信息
        int rows = sysUserMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user) {
        return sysUserMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     * @return 结果
     */
    @Transactional
    public int updateUser(SysUserEditReq userEditReq, SysUser loginUser) {
        checkUserAllowed(userEditReq.getUserId());
        checkUserDataScope(userEditReq.getUserId(), loginUser);
        sysDeptService.checkDeptDataScope(userEditReq.getDeptId(), loginUser);
        sysRoleService.checkRoleDataScope(userEditReq.getRoleIds(), loginUser);

        if (!checkUsernameUnique(userEditReq.getUserId(), userEditReq.getUsername())) {
            throw new ServiceException("修改用户'" + userEditReq.getUsername() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(userEditReq.getPhoneNumber()) && !checkPhoneUnique(userEditReq.getUserId(), userEditReq.getPhoneNumber())) {
            throw new ServiceException("修改用户'" + userEditReq.getUsername() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(userEditReq.getEmail()) && !checkEmailUnique(userEditReq.getUserId(), userEditReq.getEmail())) {
            throw new ServiceException("修改用户'" + userEditReq.getUsername() + "'失败，邮箱账号已存在");
        }

        SysUser user = new SysUser();
        BeanUtils.copyProperties(userEditReq, user);
        user.setUpdateBy(loginUser.getUsername());

        Long userId = user.getUserId();
        // 删除用户与角色关联
        sysUserRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        sysUserPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        return sysUserMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Transactional
    public void insertUserAuth(Long userId, List<Long> roleIds) {
        sysUserRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user) {
        return sysUserMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     */
    public void updateUserProfile(SysUser user) {
        sysUserMapper.updateUser(user);
    }

    public int updateUserProfile(SysUserProfileUpdateReq profileUpdateReq, SysUser loginUser) {
        loginUser.setNickName(profileUpdateReq.getNickName());
        loginUser.setEmail(profileUpdateReq.getEmail());
        loginUser.setPhoneNumber(profileUpdateReq.getPhoneNumber());
        loginUser.setSex(profileUpdateReq.getSex());

        if (StringUtils.isNotEmpty(profileUpdateReq.getPhoneNumber()) && !checkPhoneUnique(loginUser.getUserId(), profileUpdateReq.getPhoneNumber())) {
            throw new ServiceException("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(profileUpdateReq.getEmail()) && !checkEmailUnique(loginUser.getUserId(), profileUpdateReq.getEmail())) {
            throw new ServiceException("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }
        return sysUserMapper.updateUser(loginUser);
    }

    /**
     * 修改用户头像
     *
     * @param username 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String username, String avatar) {
        return sysUserMapper.updateUserAvatar(username, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user) {
        return sysUserMapper.updateUser(user);
    }

    public String selectPasswordByUserId(Long userId) {
        return sysUserMapper.selectPasswordByUserId(userId);
    }

    /**
     * 重置用户密码
     *
     * @param userId 用户Id
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(Long userId, String password) {
        return sysUserMapper.resetUserPwd(userId, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        List<Long> posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>(posts.size());
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            sysUserPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, List<Long> roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.size());
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            sysUserRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        sysUserRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        sysUserPostMapper.deleteUserPostByUserId(userId);
        return sysUserMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Transactional
    public int deleteUserByIds(List<Long> userIds, SysUser loginSysUser) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId, loginSysUser);
        }
        // 删除用户与角色关联
        sysUserRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        sysUserPostMapper.deleteUserPost(userIds);
        return sysUserMapper.deleteUserByIds(userIds);
    }

    public UserAuthRoleResp getUserAuthRole(Long userId) {
        UserAuthRoleResp resp = new UserAuthRoleResp();
        SysUser user = sysUserMapper.selectUserById(userId);
        List<SysRole> roles = sysRoleService.selectRolesByUserId(userId);
        
        resp.setUser(user);
        resp.setRoles(SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        
        return resp;
    }

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    // TODO: excel
//    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName)
//    {
//        if (StringUtils.isNull(userList) || userList.isEmpty())
//        {
//            throw new ServiceException("导入用户数据不能为空！");
//        }
//        int successNum = 0;
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
//        for (SysUser user : userList)
//        {
//            try
//            {
//                // 验证是否存在这个用户
//                SysUser u = userMapper.selectUserByUsername(user.getUsername());
//                if (StringUtils.isNull(u))
//                {
//                    BeanValidators.validateWithException(validator, user);
//                    deptService.checkDeptDataScope(user.getDeptId());
//                    String password = configService.selectConfigByKey("sys.user.initPassword");
//                    user.setPassword(SecurityUtils.encryptPassword(password));
//                    user.setCreateBy(operName);
//                    userMapper.insertUser(user);
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUsername() + " 导入成功");
//                }
//                else if (isUpdateSupport)
//                {
//                    BeanValidators.validateWithException(validator, user);
//                    checkUserAllowed(u);
//                    checkUserDataScope(u.getUserId());
//                    deptService.checkDeptDataScope(user.getDeptId());
//                    user.setUserId(u.getUserId());
//                    user.setUpdateBy(operName);
//                    userMapper.updateUser(user);
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUsername() + " 更新成功");
//                }
//                else
//                {
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUsername() + " 已存在");
//                }
//            }
//            catch (Exception e)
//            {
//                failureNum++;
//                String msg = "<br/>" + failureNum + "、账号 " + user.getUsername() + " 导入失败：";
//                failureMsg.append(msg).append(e.getMessage());
//                log.error(msg, e);
//            }
//        }
//        if (failureNum > 0)
//        {
//            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
//            throw new ServiceException(failureMsg.toString());
//        }
//        else
//        {
//            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
//        }
//        return successMsg.toString();
//    }
}
