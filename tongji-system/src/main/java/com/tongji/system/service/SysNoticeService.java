package com.tongji.system.service;

import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.PageUtils;
import com.tongji.system.entity.SysNotice;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.notice.SysNoticeAddReq;
import com.tongji.system.dto.req.notice.SysNoticeEditReq;
import com.tongji.system.dto.req.notice.SysNoticeListReq;
import com.tongji.system.mapper.SysNoticeMapper;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公告 服务层实现
 */
@Service
public class SysNoticeService {

    @Resource
    private SysNoticeMapper sysNoticeMapper;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNotice selectNoticeById(Long noticeId) {
        return sysNoticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     * @return 公告集合
     */
    public TablePage<SysNotice> selectNoticeList(SysNoticeListReq listReq) {
        return PageUtils.paginate(() -> sysNoticeMapper.selectNoticeList(listReq));
    }

    /**
     * 新增公告
     * @return 结果
     */
    public int insertNotice(SysNoticeAddReq addReq, SysUser loginUser) {
        SysNotice notice = new SysNotice();
        BeanUtils.copyProperties(addReq, notice);
        notice.setCreateBy(loginUser.getUsername());
        return sysNoticeMapper.insertNotice(notice);
    }

    /**
     * 修改公告
     * @return 结果
     */
    public int updateNotice(SysNoticeEditReq editReq, SysUser loginUser) {
        SysNotice notice = new SysNotice();
        BeanUtils.copyProperties(editReq, notice);
        notice.setUpdateBy(loginUser.getUsername());
        return sysNoticeMapper.updateNotice(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId) {
        return sysNoticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(List<Long> noticeIds) {
        return sysNoticeMapper.deleteNoticeByIds(noticeIds);
    }
}
