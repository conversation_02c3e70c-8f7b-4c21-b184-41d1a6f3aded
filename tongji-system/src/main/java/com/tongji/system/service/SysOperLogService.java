package com.tongji.system.service;

import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.PageUtils;
import com.tongji.system.entity.SysOperLog;
import com.tongji.system.dto.req.SysOperLogListReq;
import com.tongji.system.mapper.SysOperLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作日志 服务层处理
 */
@Service
public class SysOperLogService {

    @Resource
    private SysOperLogMapper sysOperLogMapper;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    public void insertOperlog(SysOperLog operLog) {
        sysOperLogMapper.insertOperlog(operLog);
    }

    /**
     * 查询系统操作日志集合
     * @return 操作日志集合
     */
    public TablePage<SysOperLog> selectOperLogList(SysOperLogListReq listReq) {
        return PageUtils.paginate(() -> sysOperLogMapper.selectOperLogList(listReq));
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(List<Long> operIds) {
        return sysOperLogMapper.deleteOperLogByIds(operIds);
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long operId) {
        return sysOperLogMapper.selectOperLogById(operId);
    }

    /**
     * 清空操作日志
     */
    public void cleanOperLog() {
        sysOperLogMapper.cleanOperLog();
    }

}
