package com.tongji.system.service;

import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.PageUtils;
import com.tongji.system.entity.SysLogininfor;
import com.tongji.system.dto.req.SysLogininforListReq;
import com.tongji.system.mapper.SysLogininforMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 */
@Service
public class SysLogininforService {

    @Resource
    private SysLogininforMapper sysLogininforMapper;

    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    public void insertLogininfor(SysLogininfor logininfor) {
        sysLogininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param listReq 访问日志对象
     * @return 登录记录集合
     */
    public TablePage<SysLogininfor> selectLogininforList(SysLogininforListReq listReq) {
        return PageUtils.paginate(() -> sysLogininforMapper.selectLogininforList(listReq));
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    public int deleteLogininforByIds(List<Long> infoIds) {
        return sysLogininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    public void cleanLogininfor() {
        sysLogininforMapper.cleanLogininfor();
    }
}
