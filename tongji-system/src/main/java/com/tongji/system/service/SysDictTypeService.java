package com.tongji.system.service;

import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.exception.ServiceException;
import com.tongji.common.utils.PageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysDictData;
import com.tongji.system.entity.SysDictType;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.dict.SysDictDataListReq;
import com.tongji.system.dto.req.dict.SysDictTypeAddReq;
import com.tongji.system.dto.req.dict.SysDictTypeEditReq;
import com.tongji.system.dto.req.dict.SysDictTypeListReq;
import com.tongji.system.mapper.SysDictDataMapper;
import com.tongji.system.mapper.SysDictTypeMapper;
import com.tongji.system.utils.DictUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 */
@Service
public class SysDictTypeService {

    @Resource
    private SysDictTypeMapper sysDictTypeMapper;

    @Resource
    private SysDictDataMapper sysDictDataMapper;

    /**
     * 项目启动时，初始化字典到缓存
     */
    @PostConstruct
    public void init() {
        loadingDictCache();
    }

    /**
     * 根据条件分页查询字典类型
     * @return 字典类型集合信息
     */
    public TablePage<SysDictType> selectDictTypeList(SysDictTypeListReq listReq) {
        return PageUtils.paginate(() -> sysDictTypeMapper.selectDictTypeList(listReq));
    }

    /**
     * 根据所有字典类型
     *
     * @return 字典类型集合信息
     */
    public List<SysDictType> selectDictTypeAll() {
        return sysDictTypeMapper.selectDictTypeAll();
    }

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    public List<SysDictData> selectDictDataByType(String dictType) {
        List<SysDictData> dictDatas = DictUtils.getDictCache(dictType);
        if (StringUtils.isNotEmpty(dictDatas)) {
            return dictDatas;
        }
        dictDatas = sysDictDataMapper.selectDictDataByType(dictType);
        if (StringUtils.isNotEmpty(dictDatas)) {
            DictUtils.setDictCache(dictType, dictDatas);
            return dictDatas;
        }
        return null;
    }

    /**
     * 根据字典类型ID查询信息
     *
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    public SysDictType selectDictTypeById(Long dictId) {
        return sysDictTypeMapper.selectDictTypeById(dictId);
    }

    /**
     * 根据字典类型查询信息
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    public SysDictType selectDictTypeByType(String dictType) {
        return sysDictTypeMapper.selectDictTypeByType(dictType);
    }

    /**
     * 批量删除字典类型信息
     *
     * @param dictIds 需要删除的字典ID
     */
    public void deleteDictTypeByIds(List<Long> dictIds) {
        for (Long dictId : dictIds) {
            SysDictType dictType = selectDictTypeById(dictId);
            if (sysDictDataMapper.countDictDataByType(dictType.getDictType()) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }
            sysDictTypeMapper.deleteDictTypeById(dictId);
            DictUtils.removeDictCache(dictType.getDictType());
        }
    }

    /**
     * 加载字典缓存数据
     */
    public void loadingDictCache() {
        SysDictDataListReq dictData = new SysDictDataListReq();
        dictData.setStatus("0");
        Map<String, List<SysDictData>> dictDataMap = sysDictDataMapper.selectDictDataList(dictData).stream().collect(Collectors.groupingBy(SysDictData::getDictType));
        for (Map.Entry<String, List<SysDictData>> entry : dictDataMap.entrySet()) {
            DictUtils.setDictCache(entry.getKey(), entry.getValue().stream().sorted(Comparator.comparing(SysDictData::getDictSort)).collect(Collectors.toList()));
        }
    }

    /**
     * 清空字典缓存数据
     */
    public void clearDictCache() {
        DictUtils.clearDictCache();
    }

    /**
     * 重置字典缓存数据
     */
    public void resetDictCache() {
        clearDictCache();
        loadingDictCache();
    }

    /**
     * 新增保存字典类型信息
     * @return 结果
     */
    public int insertDictType(SysDictTypeAddReq req, SysUser loginUser) {
        if (!checkDictTypeUnique(null, req.getDictType())) {
            throw new ServiceException("新增字典'" + req.getDictName() + "'失败，字典类型已存在");
        }

        SysDictType dict = new SysDictType();
        BeanUtils.copyProperties(req, dict);
        dict.setCreateBy(loginUser.getUsername());
        int row = sysDictTypeMapper.insertDictType(dict);
        if (row > 0) {
            DictUtils.setDictCache(dict.getDictType(), null);
        }
        return row;
    }

    /**
     * 修改保存字典类型信息
     * @return 结果
     */
    @Transactional
    public int updateDictType(SysDictTypeEditReq req, SysUser loginUser) {
        if (!checkDictTypeUnique(req.getDictId(), req.getDictType())) {
            throw new ServiceException("修改字典'" + req.getDictName() + "'失败，字典类型已存在");
        }

        SysDictType dict = new SysDictType();
        BeanUtils.copyProperties(req, dict);
        dict.setUpdateBy(loginUser.getUsername());

        SysDictType oldDict = sysDictTypeMapper.selectDictTypeById(dict.getDictId());
        sysDictDataMapper.updateDictDataType(oldDict.getDictType(), dict.getDictType());
        int row = sysDictTypeMapper.updateDictType(dict);
        if (row > 0) {
            List<SysDictData> dictDatas = sysDictDataMapper.selectDictDataByType(dict.getDictType());
            DictUtils.setDictCache(dict.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 校验字典类型称是否唯一
     * @return 结果
     */
    public boolean checkDictTypeUnique(Long dictId, String dictType) {
        long id = StringUtils.isNull(dictId) ? -1L : dictId;
        SysDictType dict = sysDictTypeMapper.checkDictTypeUnique(dictType);
        if (StringUtils.isNotNull(dict) && dict.getDictId() != id) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
}
