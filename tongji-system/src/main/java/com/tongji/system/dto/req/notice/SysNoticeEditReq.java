package com.tongji.system.dto.req.notice;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "系统通知编辑请求对象")
public class SysNoticeEditReq {

    @NotNull(message = "公告ID不能为空")
    @Schema(description = "公告ID", required = true, example = "1")
    private Long noticeId;

    @NotBlank(message = "公告标题不能为空")
    @Size(min = 0, max = 50, message = "公告标题不能超过50个字符")
    @Schema(description = "公告标题", required = true, example = "系统维护通知")
    private String noticeTitle;

    @NotBlank(message = "公告类型不能为空")
    @Schema(description = "公告类型（1通知 2公告）", required = true, example = "1")
    private String noticeType;

    @NotBlank(message = "公告内容不能为空")
    @Schema(description = "公告内容", required = true)
    private String noticeContent;

    @Schema(description = "公告状态（0正常 1关闭）", example = "0")
    private String status;

    @Schema(description = "备注")
    private String remark;
}
