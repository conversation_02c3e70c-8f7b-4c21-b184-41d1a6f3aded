package com.tongji.system.dto.resp.dept;

import java.util.List;

import com.tongji.system.domain.TreeSelect;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "部门树和选中的部门响应")
public class DeptTreeCheckedResp {

    @Schema(description = "部门树列表")
    private List<TreeSelect> depts;

    @Schema(description = "选中的部门ID列表")
    private List<Long> checkedKeys;

    public DeptTreeCheckedResp() {
    }

    public DeptTreeCheckedResp(List<TreeSelect> depts, List<Long> checkedKeys) {
        this.depts = depts;
        this.checkedKeys = checkedKeys;
    }

}
