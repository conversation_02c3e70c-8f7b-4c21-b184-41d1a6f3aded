package com.tongji.system.dto.req.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "已分配角色的用户查询请求对象")
public class SysUserAllocatedRoleQueryReq {

    @Schema(description = "角色ID", example = "1")
    private Long roleId;

    @Schema(description = "用户名称", example = "admin")
    private String username;

    @Schema(description = "手机号码", example = "13800138000")
    private String phoneNumber;
}
