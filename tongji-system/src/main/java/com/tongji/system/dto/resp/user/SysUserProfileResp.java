package com.tongji.system.dto.resp.user;

import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "系统用户个人资料响应对象")
public class SysUserProfileResp {

    @Schema(description = "用户信息，包含用户的详细属性如用户名、邮箱、手机号等")
    private SysUser user;

    @Schema(description = "用户所属角色组，多个角色以逗号分隔，例如：'管理员,普通用户'")
    private String roleGroup;

    @Schema(description = "用户所属岗位组，多个岗位以逗号分隔，例如：'技术部门主管,Java开发工程师'")
    private String postGroup;
}
