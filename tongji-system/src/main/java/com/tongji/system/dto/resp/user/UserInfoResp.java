package com.tongji.system.dto.resp.user;

import com.tongji.system.entity.SysPost;
import com.tongji.system.entity.SysRole;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "用户详细信息响应")
public class UserInfoResp {

    @Schema(description = "用户信息")
    private SysUser user;

    @Schema(description = "角色列表")
    private List<SysRole> roles;

    @Schema(description = "岗位列表")
    private List<SysPost> posts;

    @Schema(description = "选中的岗位ID列表")
    private List<Long> postIds;

    @Schema(description = "选中的角色ID列表")
    private List<Long> roleIds;
}
