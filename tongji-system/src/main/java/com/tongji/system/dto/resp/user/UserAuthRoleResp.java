package com.tongji.system.dto.resp.user;

import com.tongji.system.entity.SysRole;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "用户授权角色响应")
public class UserAuthRoleResp {

    @Schema(description = "用户信息")
    private SysUser user;

    @Schema(description = "角色列表")
    private List<SysRole> roles;
}
