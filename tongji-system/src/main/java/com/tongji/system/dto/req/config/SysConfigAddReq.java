package com.tongji.system.dto.req.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "系统配置添加请求对象")
public class SysConfigAddReq {

    /**
     * 参数名称
     */
    @NotBlank(message = "参数名称不能为空")
    @Size(min = 1, max = 100, message = "参数名称不能超过100个字符")
    @Schema(description = "参数名称", example = "系统主题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configName;

    /**
     * 参数键名
     */
    @NotBlank(message = "参数键名不能为空")
    @Size(min = 1, max = 100, message = "参数键名不能超过100个字符")
    @Schema(description = "参数键名", example = "sys.theme", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configKey;

    /**
     * 参数键值
     */
    @NotBlank(message = "参数键值不能为空")
    @Size(min = 1, max = 500, message = "参数键值不能超过500个字符")
    @Schema(description = "参数键值", example = "default", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configValue;

    /**
     * 系统内置（Y是 N否）
     */
    @Schema(description = "系统内置标志（Y是 N否）", example = "N", allowableValues = {"Y", "N"})
    private String configType;

    /**
     * 备注
     */
    @Schema(description = "备注信息")
    private String remark;

    /**
     * 文本状态（1=文本，2=富文本）
     */
    @NotNull(message = "文本状态不能为空")
    @Schema(description = "文本状态（1=文本，2=富文本）")
    private Integer state;
}
