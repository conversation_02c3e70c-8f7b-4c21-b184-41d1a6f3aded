package com.tongji.system.dto.req.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "部门查询请求对象")
public class SysDeptListReq {

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "部门状态（0正常 1停用）")
    private String status;

    @Schema(description = "父部门ID")
    private Long parentId;
}
