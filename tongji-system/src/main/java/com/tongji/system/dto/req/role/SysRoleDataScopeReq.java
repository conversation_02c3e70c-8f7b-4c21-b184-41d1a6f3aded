package com.tongji.system.dto.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "角色数据权限请求对象")
public class SysRoleDataScopeReq {

    @NotNull(message = "角色ID不能为空")
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）")
    private String dataScope;

    @Schema(description = "部门树选择项是否关联显示")
    private boolean deptCheckStrictly;

    @Schema(description = "部门组（数据权限）")
    private List<Long> deptIds;
}
