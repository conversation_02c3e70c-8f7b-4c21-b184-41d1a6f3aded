package com.tongji.system.dto.req.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户查询请求对象")
public class SysUserListReq {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户账号，用于登录的唯一标识", example = "admin")
    private String username;

    @Schema(description = "用户昵称，用户在系统中显示的名称", example = "系统管理员")
    private String nickName;

    @Schema(description = "手机号码，用于联系和接收通知", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "帐号状态（0正常 1停用）", example = "0", allowableValues = {"0", "1"})
    private String status;

    @Schema(description = "部门ID，用户所属的部门", example = "100")
    private Long deptId;

    @Schema(description = "开始创建时间，格式：yyyy-MM-dd", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginCreateTime;

    @Schema(description = "结束创建时间，格式：yyyy-MM-dd", example = "2023-12-31")
    private String endCreateTime;
}
