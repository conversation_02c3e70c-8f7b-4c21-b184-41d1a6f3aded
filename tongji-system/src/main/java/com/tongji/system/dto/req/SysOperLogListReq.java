package com.tongji.system.dto.req;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "操作日志查询请求")
public class SysOperLogListReq {

    @Schema(description = "系统模块", example = "系统管理")
    private String title;

    @Schema(description = "操作人员IP", example = "127.0.0.1")
    private Long operIp;

    @Schema(description = "操作人员", example = "admin")
    private String operName;

    @Schema(description = "业务类型（0其它 1新增 2修改 3删除）", example = "1")
    private Integer businessType;

    @Schema(description = "业务类型集合（0其它 1新增 2修改 3删除）", example = "[1,2,3]")
    private List<Integer> businessTypes;

    @Schema(description = "操作状态（0正常 1异常）", example = "0")
    private Integer status;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginCreateTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endCreateTime;

}
