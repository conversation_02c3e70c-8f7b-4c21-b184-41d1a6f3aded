package com.tongji.system.dto.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "角色状态修改请求对象")
public class SysRoleStatusChangeReq {

    @NotNull(message = "角色ID不能为空")
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色状态（0正常 1停用）")
    private String status;
}
