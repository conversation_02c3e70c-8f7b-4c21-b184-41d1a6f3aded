package com.tongji.system.dto.req.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "字典数据编辑请求对象")
public class SysDictDataEditReq {

    @NotNull(message = "字典编码不能为空")
    @Schema(description = "字典编码", required = true)
    private Long dictCode;

    @Schema(description = "字典排序")
    private Integer dictSort;

    @NotBlank(message = "字典标签不能为空")
    @Size(min = 0, max = 100, message = "字典标签长度不能超过100个字符")
    @Schema(description = "字典标签", required = true)
    private String dictLabel;

    @NotBlank(message = "字典键值不能为空")
    @Size(min = 0, max = 100, message = "字典键值长度不能超过100个字符")
    @Schema(description = "字典键值", required = true)
    private String dictValue;

    @NotBlank(message = "字典类型不能为空")
    @Size(min = 0, max = 100, message = "字典类型长���不能超过100个字符")
    @Schema(description = "字典类型", required = true)
    private String dictType;

    @Size(min = 0, max = 100, message = "样式属性长度不能超过100个字符")
    @Schema(description = "样式属性（其他样式扩展）")
    private String cssClass;

    @Schema(description = "表格字典样式")
    private String listClass;

    @Schema(description = "是否默认（Y是 N否）")
    private String isDefault;

    @Schema(description = "状态（0正常 1停用）")
    private String status;

    @Schema(description = "备注")
    private String remark;
}
