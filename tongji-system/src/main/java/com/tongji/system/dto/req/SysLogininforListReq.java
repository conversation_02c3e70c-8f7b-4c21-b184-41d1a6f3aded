package com.tongji.system.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Schema(description = "系统访问记录查询请求")
public class SysLogininforListReq {

    @Schema(description = "登录账号", example = "admin")
    private String username;

    @Schema(description = "登录IP地址", example = "***********")
    private String ipaddr;

    @Schema(description = "登录状态（0成功 1失败）", example = "0")
    private String status;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startLoginTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endLoginTime;

}
