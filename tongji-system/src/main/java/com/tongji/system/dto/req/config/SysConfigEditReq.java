package com.tongji.system.dto.req.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "系统配置编辑请求对象")
public class SysConfigEditReq {

    @Schema(description = "配置ID", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "参数ID不能为空")
    private Long configId;

    @Schema(description = "配置名称", maxLength = 100, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "参数名称不能为空")
    @Size(max = 100, message = "参数名称不能超过100个字符")
    private String configName;

    @Schema(description = "配置键名", maxLength = 100, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "参数键名不能为空")
    @Size(max = 100, message = "参数键名长度不能超过100个字符")
    private String configKey;

    @Schema(description = "配置键值", maxLength = 500, requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "参数键值不能为空")
    @Size(max = 500, message = "参数键值长度不能超过500个字符")
    private String configValue;

    @Schema(description = "备注", maxLength = 500)
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 文本状态（1=文本，2=富文本）
     */
    @NotNull(message = "文本状态不能为空")
    @Schema(description = "文本状态（1=文本，2=富文本）")
    private Integer state;
}
