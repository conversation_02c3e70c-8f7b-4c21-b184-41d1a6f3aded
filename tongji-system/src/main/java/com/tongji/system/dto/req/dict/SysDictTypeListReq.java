package com.tongji.system.dto.req.dict;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "字典类型查询请求对象")
public class SysDictTypeListReq {

    @Schema(description = "字典名称")
    private String dictName;

    @Schema(description = "字典类型")
    private String dictType;

    @Schema(description = "状态（0正常 1停用）")
    private String status;

    @Schema(description = "开始时间")
    private String beginCreateTime;

    @Schema(description = "结束时间")
    private String endCreateTime;

}
