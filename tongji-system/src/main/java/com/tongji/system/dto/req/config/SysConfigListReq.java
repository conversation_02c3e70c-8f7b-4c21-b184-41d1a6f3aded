package com.tongji.system.dto.req.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "系统配置查询请求")
public class SysConfigListReq {

    @Schema(description = "参数名称")
    private String configName;

    @Schema(description = "参数键名")
    private String configKey;

    @Schema(description = "系统内置（Y是 N否）")
    private String configType;

    @Schema(description = "开始创建时间")
    private String beginCreateTime;

    @Schema(description = "结束创建时间")
    private String endCreateTime;

    @Schema(description = "文本状态（1=文本，2=富文本）")
    private Integer state;

}
