package com.tongji.system.dto.req.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "角色查询请求对象")
public class SysRoleListReq {

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色权限字符串")
    private String roleKey;

    @Schema(description = "角色状态（0正常 1停用）")
    private String status;

    @Schema(description = "开始创建时间")
    private String beginCreateTime;

    @Schema(description = "结束创建时间")
    private String endCreateTime;
}
