package com.tongji.system.dto.req.post;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "岗位添加请求对象")
public class SysPostAddReq {

    @NotBlank(message = "岗位编码不能为空")
    @Size(min = 0, max = 64, message = "岗位编码长度不能超过64个字符")
    @Schema(description = "岗位编码", required = true, example = "coder")
    private String postCode;

    @NotBlank(message = "岗位名称不能为空")
    @Size(min = 0, max = 50, message = "岗位名称长度不能超过50个字符")
    @Schema(description = "岗位名称", required = true, example = "程序员")
    private String postName;

    @Schema(description = "显示顺序", example = "1")
    private Integer postSort;

    @Schema(description = "岗位状态（0正常 1停用）", example = "0")
    private String status;

    @Schema(description = "备注", example = "技术岗位")
    private String remark;
}
