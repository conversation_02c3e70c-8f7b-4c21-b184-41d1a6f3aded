package com.tongji.system.dto.req.user;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "用户添加请求对象")
public class SysUserAddReq {

    @NotBlank(message = "用户名称不能为空")
    @Size(min = 0, max = 30, message = "用户名称长度不能超过30个字符")
    @Schema(description = "用户名称", required = true, example = "张三")
    private String username;

    @NotBlank(message = "用户昵称不能为空")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    @Schema(description = "用户昵称", required = true, example = "zhangsan")
    private String nickName;

    @NotBlank(message = "用户密码不能为空")
    @Size(min = 6, max = 20, message = "用户密码长度必须在6-20个字符之间")
    @Schema(description = "用户密码", required = true, example = "123456")
    private String password;

    @Schema(description = "部门ID", example = "100")
    private Long deptId;

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    @Schema(description = "手机号码", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "用户性别（0男 1女 2未知）", example = "0")
    private String sex;

    @Schema(description = "帐号状态（0正常 1停用）", example = "0")
    private String status;

    @Schema(description = "角色ID列表")
    private List<Long> roleIds;

    @Schema(description = "岗位ID列表")
    private List<Long> postIds;

    @Schema(description = "备注", example = "测试用户")
    private String remark;
}
