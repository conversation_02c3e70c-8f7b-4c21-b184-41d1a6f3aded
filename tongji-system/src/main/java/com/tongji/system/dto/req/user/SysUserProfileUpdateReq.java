package com.tongji.system.dto.req.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "用户个人信息更新请求")
public class SysUserProfileUpdateReq {

    @NotBlank(message = "用户昵称不能为空")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    @Schema(description = "用户昵称", required = true, example = "张三")
    private String nickName;

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    @Schema(description = "用户邮箱", example = "<EMAIL>")
    private String email;

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    @Schema(description = "手机号码", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "用户性别（0男 1女 2未知）", example = "0")
    private String sex;
}
