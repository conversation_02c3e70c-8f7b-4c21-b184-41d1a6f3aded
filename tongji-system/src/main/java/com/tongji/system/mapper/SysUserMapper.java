package com.tongji.system.mapper;

import com.tongji.common.database.mapper.CoreMapper;
import com.tongji.system.entity.SysUser;
import com.tongji.system.dto.req.user.SysUserListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 */
public interface SysUserMapper extends CoreMapper<SysUser> {

    /**
     * 根据条件分页查询用户列表
     *
     * @param listReq 过滤条件
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUserListReq listReq);

    /**
     * 根据条件分页查询已配用户角色列表
     *
     * @return 用户信息集合信息
     */
    List<SysUser> selectAllocatedList(Long roleId, String username, String phoneNumber);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @return 用户信息集合信息
     */
    List<SysUser> selectUnallocatedList(Long roleId, String username, String phoneNumber);

    /**
     * 通过用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUsername(String username);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 修改用户头像
     *
     * @param username 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    int updateUserAvatar(@Param("username") String username, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     *
     * @param userId 用户Id
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(@Param("userId") Long userId, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(List<Long> userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param username 用户名称
     * @return 结果
     */
    SysUser checkUsernameUnique(String username);

    /**
     * 校验手机号码是否唯一
     *
     * @param phoneNumber 手机号码
     * @return 结果
     */
    SysUser checkPhoneUnique(String phoneNumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    SysUser checkEmailUnique(String email);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userId 用户ID
     * @return 结果
     */
    String selectPasswordByUserId(Long userId);
}
