package com.tongji.system.mapper;

import com.tongji.common.database.mapper.CoreMapper;
import com.tongji.system.entity.SysNotice;
import com.tongji.system.dto.req.notice.SysNoticeListReq;

import java.util.List;

/**
 * 通知公告表 数据层
 */
public interface SysNoticeMapper extends CoreMapper<SysNotice> {

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param listReq 公告信息
     * @return 公告集合
     */
    List<SysNotice> selectNoticeList(SysNoticeListReq listReq);

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    int insertNotice(SysNotice notice);

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    int updateNotice(SysNotice notice);

    /**
     * 批量删除公告
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    int deleteNoticeByIds(List<Long> noticeIds);
}
