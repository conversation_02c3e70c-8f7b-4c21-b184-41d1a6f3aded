package com.tongji.system.domain.server;

import com.tongji.common.utils.Arith;
import com.tongji.common.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.lang.management.ManagementFactory;

/**
 * JVM相关信息
 */
@Data
@Schema(description = "JVM相关信息")
public class Jvm {

    /**
     * 当前JVM占用的内存总数(M)
     */
    @Schema(description = "当前JVM占用的内存总数(M)")
    private double total;

    /**
     * JVM最大可用内存总数(M)
     */
    @Schema(description = "JVM最大可用内存总数(M)")
    private double max;

    /**
     * JVM空闲内存(M)
     */
    @Schema(description = "JVM空闲内存(M)")
    private double free;

    /**
     * JDK版本
     */
    @Schema(description = "JDK版本")
    private String version;

    /**
     * JDK路径
     */
    @Schema(description = "JDK路径")
    private String home;

    @Schema(description = "获取JVM占用的内存总数(M)")
    public double getTotal() {
        return Arith.div(total, (1024 * 1024), 2);
    }

    @Schema(description = "获取JVM最大可用内存总数(M)")
    public double getMax() {
        return Arith.div(max, (1024 * 1024), 2);
    }

    @Schema(description = "获取JVM空闲内存(M)")
    public double getFree() {
        return Arith.div(free, (1024 * 1024), 2);
    }

    @Schema(description = "获取JVM已使用内存(M)")
    public double getUsed() {
        return Arith.div(total - free, (1024 * 1024), 2);
    }

    @Schema(description = "获取JVM内存使用率")
    public double getUsage() {
        return Arith.mul(Arith.div(total - free, total, 4), 100);
    }

    /**
     * 获取JDK名称
     */
    @Schema(description = "获取JDK名称")
    public String getName() {
        return ManagementFactory.getRuntimeMXBean().getVmName();
    }

    /**
     * JDK启动时间
     */
    @Schema(description = "JDK启动时间")
    public String getStartTime() {
        return DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.getServerStartDate());
    }

    /**
     * JDK运行时间
     */
    @Schema(description = "JDK运行时间")
    public String getRunTime() {
        return DateUtils.timeDistance(DateUtils.getNowDate(), DateUtils.getServerStartDate());
    }

    /**
     * 运行参数
     */
    @Schema(description = "运行参数")
    public String getInputArgs() {
        return ManagementFactory.getRuntimeMXBean().getInputArguments().toString();
    }
}
