package com.tongji.system.domain.server;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统相关信息
 */
@Data
@Schema(description = "系统相关信息")
public class Sys {

    /**
     * 服务器名称
     */
    @Schema(description = "服务器名称")
    private String computerName;

    /**
     * 服务器Ip
     */
    @Schema(description = "服务器IP地址")
    private String computerIp;

    /**
     * 项目路径
     */
    @Schema(description = "项目路径")
    private String userDir;

    /**
     * 操作系统
     */
    @Schema(description = "操作系统名称")
    private String osName;

    /**
     * 系统架构
     */
    @Schema(description = "系统架构")
    private String osArch;

}
