package com.tongji.system.domain.server;

import com.tongji.common.utils.Arith;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * CPU相关信息
 */
@Data
@Schema(description = "CPU相关信息")
public class Cpu {

    /**
     * 核心数
     */
    @Schema(description = "CPU核心数")
    private int cpuNum;

    /**
     * CPU总的使用率
     */
    @Schema(description = "CPU总的使用率")
    private double total;

    /**
     * CPU系统使用率
     */
    @Schema(description = "CPU系统使用率")
    private double sys;

    /**
     * CPU用户使用率
     */
    @Schema(description = "CPU用户使用率")
    private double used;

    /**
     * CPU当前等待率
     */
    @Schema(description = "CPU当前等待率")
    private double wait;

    /**
     * CPU当前空闲率
     */
    @Schema(description = "CPU当前空闲率")
    private double free;

    @Schema(description = "获取CPU总使用率（百分比）")
    public double getTotal() {
        return Arith.round(Arith.mul(total, 100), 2);
    }

    @Schema(description = "获取CPU系统使用率（百分比）")
    public double getSys() {
        return Arith.round(Arith.mul(sys / total, 100), 2);
    }

    @Schema(description = "获取CPU用户使用率（百分比）")
    public double getUsed() {
        return Arith.round(Arith.mul(used / total, 100), 2);
    }

    @Schema(description = "获取CPU等待率（百分比）")
    public double getWait() {
        return Arith.round(Arith.mul(wait / total, 100), 2);
    }

    @Schema(description = "获取CPU空闲率（百分比）")
    public double getFree() {
        return Arith.round(Arith.mul(free / total, 100), 2);
    }

}
