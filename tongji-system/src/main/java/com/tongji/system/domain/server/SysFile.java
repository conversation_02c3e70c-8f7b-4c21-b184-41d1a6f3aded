package com.tongji.system.domain.server;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统文件相关信息
 */
@Data
@Schema(description = "系统文件相关信息")
public class SysFile {

    /**
     * 盘符路径
     */
    @Schema(description = "盘符路径")
    private String dirName;

    /**
     * 盘符类型
     */
    @Schema(description = "盘符类型")
    private String sysTypeName;

    /**
     * 文件类型
     */
    @Schema(description = "文件类型")
    private String typeName;

    /**
     * 总大小
     */
    @Schema(description = "总大小")
    private String total;

    /**
     * 剩余大小
     */
    @Schema(description = "剩余大小")
    private String free;

    /**
     * 已经使用量
     */
    @Schema(description = "已使用量")
    private String used;

    /**
     * 资源的使用率
     */
    @Schema(description = "资源使用率（百分比）")
    private double usage;

}
