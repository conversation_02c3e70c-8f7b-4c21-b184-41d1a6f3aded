package com.tongji.system.domain.server;

import com.tongji.common.utils.Arith;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 內存相关信息
 */
@Data
@Schema(description = "内存相关信息")
public class Mem {

    /**
     * 内存总量
     */
    @Schema(description = "内存总量")
    private double total;

    /**
     * 已用内存
     */
    @Schema(description = "已用内存")
    private double used;

    /**
     * 剩余内存
     */
    @Schema(description = "剩余内存")
    private double free;

    @Schema(description = "获取内存总量(GB)")
    public double getTotal() {
        return Arith.div(total, (1024 * 1024 * 1024), 2);
    }

    @Schema(description = "获取已用内存(GB)")
    public double getUsed() {
        return Arith.div(used, (1024 * 1024 * 1024), 2);
    }

    @Schema(description = "获取剩余内存(GB)")
    public double getFree() {
        return Arith.div(free, (1024 * 1024 * 1024), 2);
    }

    @Schema(description = "获取内存使用率")
    public double getUsage() {
        return Arith.mul(Arith.div(used, total, 4), 100);
    }
}
