package com.tongji.system.domain;

import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

/**
 * Tree基类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "树形结构基类")
public class TreeEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 父菜单名称
     */
    @Schema(description = "父菜单名称")
    private String parentName;

    /**
     * 父菜单ID
     */
    @Schema(description = "父菜单ID")
    private Long parentId;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer orderNum;

    /**
     * 祖级列表
     */
    @Schema(description = "祖级列表")
    private String ancestors;

    /**
     * 子部门
     */
    @Schema(description = "子节点列表")
    private List<?> children = new ArrayList<>();

}
