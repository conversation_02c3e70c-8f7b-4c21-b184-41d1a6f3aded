package com.tongji.system.entity;

import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 参数配置表 sys_config
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "系统配置实体")
public class SysConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 参数主键
     */
    @Schema(description = "配置ID")
    private Long configId;

    /**
     * 参数名称
     */
    @NotBlank(message = "参数名称不能为空")
    @Size(min = 1, max = 100, message = "参数名称不能超过100个字符")
    @Schema(description = "配置名称")
    private String configName;

    /**
     * 参数键名
     */
    @NotBlank(message = "参数键名长度不能为空")
    @Size(min = 1, max = 100, message = "参数键名长度不能超过100个字符")
    @Schema(description = "配置键名")
    private String configKey;

    /**
     * 参数键值
     */
    @NotBlank(message = "参数键值不能为空")
    @Size(min = 1, max = 500, message = "参数键值长度不能超过500个字符")
    @Schema(description = "配置键值")
    private String configValue;

    /**
     * 系统内置（Y是 N否）
     */
    @Schema(description = "是否系统内置（Y是 N否）", allowableValues = {"Y", "N"})
    private String configType;

    /**
     * 文本状态（1=文本，2=富文本）
     */
    @Schema(description = "文本状态（1=文本，2=富文本）")
    private Integer state;

}
