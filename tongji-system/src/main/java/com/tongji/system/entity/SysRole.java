package com.tongji.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;
import java.util.Set;

/**
 * 角色表 sys_role
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "角色信息")
public class SysRole extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @TableId
    @Schema(description = "角色ID")
    private Long roleId;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 1, max = 30, message = "角色名称长度不能超过30个字符")
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色权限
     */
    @NotBlank(message = "权限字符不能为空")
    @Size(min = 1, max = 100, message = "权限字符长度不能超过100个字符")
    @Schema(description = "角色权限字符")
    private String roleKey;

    /**
     * 角色排序
     */
    @NotNull(message = "显示顺序不能为空")
    @Schema(description = "角色排序")
    private Integer roleSort;

    /**
     * 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
     */
    @Schema(description = "数据范围：1=所有数据权限，2=自定义数据权限，3=本部门数据权限，4=本部门及以下数据权限，5=仅本人数据权限")
    private String dataScope;

    /**
     * 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）
     */
    @Schema(description = "菜单树选择项是否关联显示：false=父子不互相关联显示，true=父子互相关联显示")
    private boolean menuCheckStrictly;

    /**
     * 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）
     */
    @Schema(description = "部门树选择项是否关联显示：false=父子不互相关联显示，true=父子互相关联显示")
    private boolean deptCheckStrictly;

    /**
     * 角色状态（0正常 1停用）
     */
    @Schema(description = "角色状态：0=正常，1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Schema(description = "删除标志：0=存在，2=删除")
    private String delFlag;

    /**
     * 用户是否存在此角色标识 默认不存在
     */
    @Schema(description = "用户是否存在此角色标识，true=存在，false=不存在")
    private boolean flag = false;

    /**
     * 菜单组
     */
    @Schema(description = "菜单ID列表")
    private List<Long> menuIds;

    /**
     * 部门组（数据权限）
     */
    @Schema(description = "部门ID列表（数据权限）")
    private List<Long> deptIds;

    /**
     * 角色菜单权限
     */
    @Schema(description = "角色菜单权限")
    private Set<String> permissions;

    public SysRole() {}

    public SysRole(Long roleId) {
        this.roleId = roleId;
    }

    @Schema(description = "判断是否为管理员")
    public static boolean isAdmin(Long roleId) {
        return roleId != null && 1L == roleId;
    }

    @Schema(description = "判断当前角色是否为管理员")
    public boolean isAdmin() {
        return isAdmin(this.roleId);
    }

}
