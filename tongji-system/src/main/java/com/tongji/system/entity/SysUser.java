package com.tongji.system.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import com.tongji.common.xss.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户对象")
public class SysUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableId
    private Long userId;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 用户账号
     */
    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 1, max = 30, message = "用户账号长度不能超过30个字符")
    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;

    /**
     * 用户昵称
     */
    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 1, max = 30, message = "用户昵称长度不能超过30个字符")
    @Schema(description = "用户昵称")
    private String nickName;

    /**
     * 用户邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(min = 1, max = 50, message = "邮箱长度不能超过50个字符")
    @Schema(description = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @Size(min = 1, max = 11, message = "手机号码长度不能超过11个字符")
    @Schema(description = "手机号码")
    private String phoneNumber;

    /**
     * 用户性别
     */
    @Schema(description = "用户性别：0=男，1=女，2=未知")
    private String sex;

    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String avatar;

    /**
     * 密码
     */
    @JSONField(serialize = false)
    @Schema(description = "密码", hidden = true)
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @Schema(description = "帐号状态：0=正常，1=停用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @Schema(description = "删除标志：0=存在，2=删除")
    private String delFlag;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private Date loginDate;

    /**
     * 部门对象
     */
    @Schema(description = "部门对象")
    private SysDept dept;

    /**
     * 角色对象
     */
    @Schema(description = "角色对象列表")
    private List<SysRole> roles;

    /**
     * 角色组
     */
    @Schema(description = "角色ID数组")
    private List<Long> roleIds;

    /**
     * 岗位组
     */
    @Schema(description = "岗位ID数组")
    private List<Long> postIds;

    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private Long roleId;

    public SysUser() {}

    public SysUser(Long userId) {
        this.userId = userId;
    }

    @Schema(description = "判断是否为管理员")
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    @Schema(description = "判断当前用户是否为管理员")
    public boolean isAdmin() {
        return isAdmin(this.userId);
    }

}
