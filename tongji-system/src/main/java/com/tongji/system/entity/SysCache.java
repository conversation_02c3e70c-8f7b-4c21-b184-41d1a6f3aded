package com.tongji.system.entity;

import com.tongji.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 缓存信息
 */
@Data
@Schema(description = "缓存信息")
public class SysCache {

    @Schema(description = "缓存名称")
    private String cacheName = "";

    @Schema(description = "缓存键名")
    private String cacheKey = "";

    @Schema(description = "缓存内容")
    private String cacheValue = "";

    @Schema(description = "备注")
    private String remark = "";

    public SysCache() {

    }

    public SysCache(String cacheName, String remark) {
        this.cacheName = cacheName;
        this.remark = remark;
    }

    public SysCache(String cacheName, String cacheKey, String cacheValue) {
        this.cacheName = StringUtils.replace(cacheName, ":", "");
        this.cacheKey = StringUtils.replace(cacheKey, cacheName, "");
        this.cacheValue = cacheValue;
    }

}
