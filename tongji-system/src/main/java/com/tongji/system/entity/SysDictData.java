package com.tongji.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 字典数据表 sys_dict_data
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "字典数据信息")
public class SysDictData extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    @TableId
    @Schema(description = "字典编码")
    private Long dictCode;

    /**
     * 字典排序
     */
    @Schema(description = "字典排序")
    private Long dictSort;

    /**
     * 字典标签
     */
    @NotBlank(message = "字典标签不能为空")
    @Size(min = 1, max = 100, message = "字典标签长度不能超过100个字符")
    @Schema(description = "字典标签")
    private String dictLabel;

    /**
     * 字典键值
     */
    @NotBlank(message = "字典键值不能为空")
    @Size(min = 1, max = 100, message = "字典键值长度不能超过100个字符")
    @Schema(description = "字典键值")
    private String dictValue;

    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 1, max = 100, message = "字典类型长度不能超过100个字符")
    @Schema(description = "字典类型")
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @Size(min = 1, max = 100, message = "样式属性长度不能超过100个字符")
    @Schema(description = "样式属性（其他样式扩展）")
    private String cssClass;

    /**
     * 表格字典样式
     */
    @Schema(description = "表格字典样式")
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @Schema(description = "是否默认：Y=是，N=否")
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description = "状态：0=正常，1=停用")
    private String status;

}
