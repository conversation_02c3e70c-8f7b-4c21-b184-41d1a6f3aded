package com.tongji.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import com.tongji.common.xss.Xss;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 通知公告表 sys_notice
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "通知公告信息")
public class SysNotice extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 公告ID
     */
    @TableId
    @Schema(description = "公告ID")
    private Long noticeId;

    /**
     * 公告标题
     */
    @Xss(message = "公告标题不能包含脚本字符")
    @NotBlank(message = "公告标题不能为空")
    @Size(min = 1, max = 50, message = "公告标题不能超过50个字符")
    @Schema(description = "公告标题", maxLength = 50, requiredMode = Schema.RequiredMode.REQUIRED)
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    @Schema(description = "公告类型：1=通知，2=公告")
    private String noticeType;

    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    @Schema(description = "公告状态：0=正常，1=关闭")
    private String status;

}
