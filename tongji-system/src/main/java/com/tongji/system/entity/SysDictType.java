package com.tongji.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 字典类型表 sys_dict_type
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "字典类型信息")
public class SysDictType extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典主键
     */
    @TableId
    @Schema(description = "字典主键")
    private Long dictId;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Size(min = 1, max = 100, message = "字典类型名称长度不能超过100个字符")
    @Schema(description = "字典名称")
    private String dictName;

    /**
     * 字典类型
     */
    @NotBlank(message = "字典类型不能为空")
    @Size(min = 1, max = 100, message = "字典类型类型长度不能超过100个字符")
    @Pattern(regexp = "^[a-z][a-z0-9_]*$", message = "字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）")
    @Schema(description = "字典类型（以字母开头，只能包含小写字母、数字、下划线）")
    private String dictType;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description = "状态：0=正常，1=停用")
    private String status;

}
