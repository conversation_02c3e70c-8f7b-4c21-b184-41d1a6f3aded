package com.tongji.system.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.tongji.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 岗位表 sys_post
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "岗位信息")
public class SysPost extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 岗位序号
     */
    @TableId
    @Schema(description = "岗位序号")
    private Long postId;

    /**
     * 岗位编码
     */
    @NotBlank(message = "岗位编码不能为空")
    @Size(min = 1, max = 64, message = "岗位编码长度不能超过64个字符")
    @Schema(description = "岗位编码")
    private String postCode;

    /**
     * 岗位名称
     */
    @NotBlank(message = "岗位名称不能为空")
    @Size(min = 1, max = 50, message = "岗位名称长度不能超过50个字符")
    @Schema(description = "岗位名称")
    private String postName;

    /**
     * 岗位排序
     */
    @NotNull(message = "显示顺序不能为空")
    @Schema(description = "岗位排序")
    private Integer postSort;

    /**
     * 状态（0正常 1停用）
     */
    @Schema(description = "状态：0=正常，1=停用")
    private String status;

    /**
     * 用户是否存在此岗位标识 默认不存在
     */
    @Schema(description = "用户是否存在此岗位标识，true=存在，false=不存在")
    private boolean flag = false;

}
