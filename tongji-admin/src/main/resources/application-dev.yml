# 项目相关配置
app:
  # 名称
  name: tongji
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2024
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  profile: /data/tongji/uploadfiles

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 18087
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
  # nginx中配置了代理，需要设置为framework，并设置proxy_set_header X-Forwarded-Prefix "/<path>";
  # 否则访问不了swagger和knife4j
  forward-headers-strategy: framework

# 日志配置
logging:
  level:
    com.tongji: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: jdbc:mysql://*************:3306/tongji?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&allowMultiQueries=true
        username: root
        password: Q123456q@
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: tongji
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    # redis 配置
    redis:
      # 地址
      host: r-bp1m2dmndwoc19giwcpd.redis.rds.aliyuncs.com
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 27
      # 密码
      password: r-bp1m2dmndwoc19giwc:Q123456q@
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: SA-Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 43200

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.tongji.**.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml, classpath*:mapper/**/*Mapper.oth.xml
  configuration:
    cache-enabled: true
    use-generated-keys: true
    default-executor-type: simple
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      insert-strategy: ignored
      update-strategy: ignored

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'common'
      display-name: '通用模块'
      paths-to-match: '/**'
      packages-to-scan: com.tongji.web.controller.common
    - group: 'monitor'
      display-name: '监控模块'
      paths-to-match: '/**'
      packages-to-scan: com.tongji.web.controller.monitor
    - group: 'system'
      display-name: '系统模块'
      paths-to-match: '/**'
      packages-to-scan: com.tongji.web.controller.system
    - group: 'logic'
      display-name: '业务模块'
      paths-to-match: '/**'
      packages-to-scan: com.tongji.web.controller.logic

knife4j:
  # 是否是生产模式
  production: false
  # 是否启用增强模式
  enable: true
  basic:
    enable: true
    username: admin
    password: 123456
  setting:
    language: zh_cn
    enable-version: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
