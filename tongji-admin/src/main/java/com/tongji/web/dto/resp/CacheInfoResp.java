package com.tongji.web.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Properties;

@Data
@Schema(description = "缓存信息响应")
public class CacheInfoResp {

    @Schema(description = "Redis信息")
    private Properties info;

    @Schema(description = "数据库大小")
    private Object dbSize;

    @Schema(description = "命令统计")
    private List<CommandStat> commandStats;

    @Data
    @Schema(description = "命令统计信息")
    public static class CommandStat {
        @Schema(description = "命令名称")
        private String name;

        @Schema(description = "命令调用次数")
        private String value;
    }
}
