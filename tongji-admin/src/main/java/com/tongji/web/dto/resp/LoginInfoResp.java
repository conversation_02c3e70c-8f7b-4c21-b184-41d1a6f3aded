package com.tongji.web.dto.resp;

import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
@Schema(description = "登录用户信息响应")
public class LoginInfoResp {

    @Schema(description = "用户信息")
    private SysUser user;

    @Schema(description = "角色集合")
    private Set<String> roles;

    @Schema(description = "权限集合")
    private Set<String> permissions;

    public LoginInfoResp(SysUser user, Set<String> roles, Set<String> permissions) {
        this.user = user;
        this.roles = roles;
        this.permissions = permissions;
    }
}
