package com.tongji.web.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "登录响应")
public class LoginResp {

    @Schema(description = "访问令牌")
    private String token;

    // 可以根据需要添加其他字段，例如：
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String username;

    // 构造方法
    public LoginResp(String token) {
        this.token = token;
    }

    // 如果需要其他字段，可以添加相应的构造方法
    public LoginResp(String token, Long userId, String username) {
        this.token = token;
        this.userId = userId;
        this.username = username;
    }
}
