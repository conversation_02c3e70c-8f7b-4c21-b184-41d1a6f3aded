package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.utils.StringUtils;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysDept;
import com.tongji.system.dto.req.dept.SysDeptAddReq;
import com.tongji.system.dto.req.dept.SysDeptEditReq;
import com.tongji.system.dto.req.dept.SysDeptListReq;
import com.tongji.security.model.LoginUser;
import com.tongji.system.service.SysDeptService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门信息
 */
@RestController
@RequestMapping("/system/dept")
@Tag(name = "部门管理", description = "处理部门相关的CRUD操作")
public class SysDeptController extends BaseController {

    @Resource
    private SysDeptService sysDeptService;

    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    @Operation(summary = "获取部门列表", description = "根据查询条件获取部门列表，需要system:dept:list权限")
    public R<List<SysDept>> list(@Parameter(description = "部门查询条件") SysDeptListReq listReq) {
        return successByData(sysDeptService.listDepts(listReq));
    }

    /**
     * 查询部门列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list/exclude/{deptId}")
    @Operation(summary = "查询部门列表（排除节点）", description = "获取部门列表，但排除指定节点及其子节点，需要system:dept:list权限")
    public R<List<SysDept>> excludeChild(@Parameter(description = "要排除的部门ID") @PathVariable(required = false) Long deptId) {
        List<SysDept> depts = sysDeptService.listDepts(new SysDeptListReq());
        depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return successByData(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    @Operation(summary = "获取部门详细信息", description = "根据部门ID获取部门详细信息，需要system:dept:query权限")
    public R<SysDept> getInfo(@Parameter(description = "部门ID") @PathVariable Long deptId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysDeptService.checkDeptDataScope(deptId, loginUser);
        return successByData(sysDeptService.queryDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增部门", description = "新增一个部门，需要system:dept:add权限。业务类型：INSERT")
    public R<Object> add(@Parameter(description = "新增部门的请求体") @Validated @RequestBody SysDeptAddReq addReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysDeptService.addDept(addReq, loginUser));
    }

    /**
     * 修改部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改部门", description = "修改现有部门信息，需要system:dept:edit权限。业务类型：UPDATE")
    public R<Object> edit(@Parameter(description = "修改部门的请求体") @Validated @RequestBody SysDeptEditReq editReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysDeptService.updateDept(editReq, loginUser));
    }

    /**
     * 删除部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    @Operation(summary = "删除部门", description = "根据部门ID删除部门，需要system:dept:remove权限。业务类型：DELETE。注意：存在下级部门或部门中存在用户时不允许删除")
    public R<Object> remove(@Parameter(description = "要删除的部门ID") @PathVariable Long deptId) {
        if (sysDeptService.hasChildByDeptId(deptId)) {
            return warnByMsg("存在下级部门,不允许删除");
        }
        if (sysDeptService.checkDeptExistUser(deptId)) {
            return warnByMsg("部门存在用户,不允许删除");
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysDeptService.checkDeptDataScope(deptId, loginUser);
        return toAjax(sysDeptService.deleteDeptById(deptId));
    }
}
