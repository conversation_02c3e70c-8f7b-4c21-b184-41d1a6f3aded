package com.tongji.web.controller.system;

import com.tongji.common.annotation.Anonymous;
import com.tongji.common.core.domain.R;
import com.tongji.common.utils.StringUtils;
import com.tongji.web.dto.req.RegisterReq;
import com.tongji.system.service.SysConfigService;
import com.tongji.web.controller.BaseController;
import com.tongji.web.service.SysRegisterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 注册验证
 */
@RestController
@Tag(name = "用户注册", description = "处理用户注册相关的操作")
public class SysRegisterController extends BaseController {

    @Resource
    private SysRegisterService sysRegisterService;

    @Resource
    private SysConfigService sysConfigService;

    @Anonymous
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "处理新用户的注册请求，包括验证系统是否开启注册功能")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "注册成功"),
        @ApiResponse(responseCode = "400", description = "注册失败，返回错误信息")
    })
    public R<Object> register(
            @Parameter(description = "用户注册信息", required = true) @RequestBody RegisterReq user) {
        if (!("true".equals(sysConfigService.queryConfigByKey("sys.account.registerUser")))) {
            return errorByMsg("当前系统没有开启注册功能！");
        }
        String msg = sysRegisterService.register(user);
        return StringUtils.isEmpty(msg) ? success() : errorByMsg(msg);
    }
}
