package com.tongji.web.controller.logic;

import com.tongji.common.core.domain.R;
import com.tongji.common.core.dto.IdReq;
import com.tongji.common.core.page.TablePage;
import com.tongji.logic.dto.req.sheet.SheetAddReq;
import com.tongji.logic.dto.req.sheet.SheetListReq;
import com.tongji.logic.dto.req.sheet.SheetUpdateReq;
import com.tongji.logic.entity.task.TSheet;
import com.tongji.logic.service.SheetService;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "表单管理")
@RestController
@RequestMapping("/logic/sheet")
public class SheetController {

    @Resource
    private SheetService sheetService;

    @Operation(summary = "查询表单列表")
    @PreAuthorize("@ss.hasPermi('logic:sheet:listPage')")
    @GetMapping("/listPage")
    public R<TablePage<TSheet>> listPage(SheetListReq req) {
        return R.successByData(sheetService.queryPage(req));
    }

    @Operation(summary = "获取表单详情")
    @PreAuthorize("@ss.hasPermi('logic:sheet:detail')")
    @GetMapping(value = "/{sheetId}")
    public R<TSheet> getInfo(@Parameter(description = "表单ID") @PathVariable Long sheetId) {
        return R.successByData(sheetService.queryDetail(sheetId));
    }

    @Operation(summary = "添加表单记录")
    @PreAuthorize("@ss.hasPermi('logic:sheet:add')")
    @PostMapping("/add")
    public R<Object> add(@Validated @RequestBody SheetAddReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        sheetService.add(req, user);
        return R.success();
    }

    @Operation(summary = "修改表单记录")
    @PreAuthorize("@ss.hasPermi('logic:sheet:update')")
    @PostMapping("/update")
    public R<Object> update(@Validated @RequestBody SheetUpdateReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        sheetService.update(req, user);
        return R.success();
    }

    @Operation(summary = "删除表单记录")
    @PreAuthorize("@ss.hasPermi('logic:sheet:delete')")
    @PostMapping("/delete")
    public R<Object> delete(@Validated @RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        sheetService.delete(req.getId(), user);
        return R.success();
    }

}
