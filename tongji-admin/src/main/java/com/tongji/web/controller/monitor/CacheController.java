package com.tongji.web.controller.monitor;

import com.google.protobuf.ServiceException;
import com.tongji.common.constant.CacheConstants;
import com.tongji.common.core.domain.R;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysCache;
import com.tongji.web.dto.resp.CacheInfoResp;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.redis.connection.DefaultedRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 缓存监控
 */
@Tag(name = "缓存监控", description = "缓存监控相关接口")
@RestController
@RequestMapping("/monitor/cache")
public class CacheController {

    private final static List<SysCache> caches = new ArrayList<>();

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    static {
        caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
        caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
        caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
        caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
        caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
        caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
        caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
    }

    @SuppressWarnings("deprecation")
    @Operation(summary = "获取缓存信息", description = "获取Redis缓存的详细信息")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public R<CacheInfoResp> getInfo() throws Exception {
        CacheInfoResp cacheInfoResp = new CacheInfoResp();

        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) DefaultedRedisConnection::info);
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) DefaultedRedisConnection::dbSize);

        cacheInfoResp.setInfo(info);
        cacheInfoResp.setDbSize(dbSize);

        List<CacheInfoResp.CommandStat> commandStatList = new ArrayList<>();
        if(commandStats == null) {
            throw new ServiceException("获取Redis缓存信息失败");
        }
        commandStats.stringPropertyNames().forEach(key -> {
            CacheInfoResp.CommandStat stat = new CacheInfoResp.CommandStat();
            String property = commandStats.getProperty(key);
            stat.setName(StringUtils.removeStart(key, "cmdstat_"));
            stat.setValue(StringUtils.substringBetween(property, "calls=", ",usec"));
            commandStatList.add(stat);
        });
        cacheInfoResp.setCommandStats(commandStatList);

        return R.successByData(cacheInfoResp);
    }

    @Operation(summary = "获取缓存名称列表", description = "获取所有缓存的名称列表")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getNames")
    public R<List<SysCache>> cache() {
        return R.successByData(caches);
    }

    @Operation(summary = "获取缓存键", description = "根据缓存名称获取缓存键列表")
    @Parameter(name = "cacheName", description = "缓存名称", required = true)
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getKeys/{cacheName}")
    public R<Set<String>> getCacheKeys(@PathVariable String cacheName) {
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        assert cacheKeys != null;
        return R.successByData(new TreeSet<>(cacheKeys));
    }

    @Operation(summary = "获取缓存值", description = "根据缓存名称和键获取缓存值")
    @Parameter(name = "cacheName", description = "缓存名称", required = true)
    @Parameter(name = "cacheKey", description = "缓存键", required = true)
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public R<SysCache> getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey) {
        Object cacheValue = redisTemplate.opsForValue().get(cacheKey);
        SysCache sysCache = new SysCache(cacheName, cacheKey, cacheValue == null ? "" : cacheValue.toString());
        return R.successByData(sysCache);
    }

    @Operation(summary = "清除指定名称的缓存", description = "清除指定名称的所有缓存")
    @Parameter(name = "cacheName", description = "缓存名称", required = true)
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheName/{cacheName}")
    public R<Object> clearCacheName(@PathVariable String cacheName) {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        assert cacheKeys != null;
        redisTemplate.delete(cacheKeys);
        return R.success();
    }

    @Operation(summary = "清除指定键的缓存", description = "清除指定键的缓存")
    @Parameter(name = "cacheKey", description = "缓存键", required = true)
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    public R<Object> clearCacheKey(@PathVariable String cacheKey) {
        redisTemplate.delete(cacheKey);
        return R.success();
    }

    @Operation(summary = "清除所有缓存", description = "清除Redis中的所有缓存")
    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheAll")
    public R<Object> clearCacheAll() {
        Collection<String> cacheKeys = redisTemplate.keys("*");
        assert cacheKeys != null;
        redisTemplate.delete(cacheKeys);
        return R.success();
    }
}