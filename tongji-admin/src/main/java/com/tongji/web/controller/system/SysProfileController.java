package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.config.AppConfig;
import com.tongji.common.core.domain.R;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.utils.file.FileUploadUtils;
import com.tongji.common.utils.file.MimeTypeUtils;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.security.model.LoginUser;
import com.tongji.system.dto.req.user.SysUserProfileUpdateReq;
import com.tongji.system.dto.resp.user.AvatarUploadResp;
import com.tongji.system.dto.resp.user.SysUserProfileResp;
import com.tongji.system.service.SysUserService;
import com.tongji.web.controller.BaseController;
import com.tongji.web.service.TokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 个人信息 业务处理
 */
@RestController
@RequestMapping("/system/user/profile")
@Tag(name = "个人信息管理", description = "处理用户个人信息相关的操作")
public class SysProfileController extends BaseController {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private TokenService tokenService;

    /**
     * 个人信息
     */
    @GetMapping
    @Operation(summary = "获取个人信息", description = "获取当前登录用户的详细个人信息，包括用户基本信息、角色组和岗位组")
    @ApiResponse(responseCode = "200", description = "成功获取个人信息")
    public R<SysUserProfileResp> profile() {
        LoginUser loginUser = getLoginUser();

        SysUserProfileResp profileResp = new SysUserProfileResp();
        profileResp.setUser(loginUser);
        profileResp.setRoleGroup(sysUserService.selectUserRoleGroup(loginUser.getUserId()));
        profileResp.setPostGroup(sysUserService.selectUserPostGroup(loginUser.getUserId()));

        return R.successByData(profileResp);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "更新个人信息", description = "修改当前登录用户的个人信息")
    @ApiResponse(responseCode = "200", description = "成功更新个人信息")
    @ApiResponse(responseCode = "500", description = "更新个人信息失败")
    public R<Object> updateProfile(@RequestBody @Validated SysUserProfileUpdateReq profileUpdateReq) {
        LoginUser loginUser = getLoginUser();

        if (sysUserService.updateUserProfile(profileUpdateReq, loginUser) > 0) {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return errorByMsg("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    @Operation(summary = "重置密码", description = "修改当前登录用户的密码")
    @ApiResponse(responseCode = "200", description = "成功重置密码")
    @ApiResponse(responseCode = "500", description = "重置密码失败")
    public R<Object> updatePwd(
            @Parameter(description = "旧密码", required = true) @RequestParam String oldPassword,
            @Parameter(description = "新密码", required = true) @RequestParam String newPassword
    ) {
        LoginUser loginUser = getLoginUser();
        if (!SecurityUtils.matchesPassword(oldPassword, loginUser.getCachePassword())) {
            return errorByMsg("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, loginUser.getCachePassword())) {
            return errorByMsg("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (sysUserService.resetUserPwd(loginUser.getUserId(), newPassword) > 0) {
            // 更新缓存用户密码
            loginUser.setPassword(newPassword);
            loginUser.setCachePassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return errorByMsg("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    @Operation(summary = "上传头像", description = "上传并更新当前登录用户的头像")
    @ApiResponse(responseCode = "200", description = "成功上传头像")
    @ApiResponse(responseCode = "500", description = "上传头像失败")
    public R<AvatarUploadResp> avatar(
            @Parameter(description = "头像文件", required = true) @RequestParam("avatarfile") MultipartFile file)
            throws Exception {
        if (!file.isEmpty()) {
            LoginUser loginUser = getLoginUser();
            String avatar = FileUploadUtils.upload(AppConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
            if (sysUserService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                AvatarUploadResp resp = new AvatarUploadResp();
                resp.setImgUrl(avatar);
                // 更新缓存用户头像
                loginUser.setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return R.successByData(resp);
            }
        }
        return errorByMsg("上传图片异常，请联系管理员");
    }
}
