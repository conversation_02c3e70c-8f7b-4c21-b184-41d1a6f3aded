package com.tongji.web.controller.system;

import com.tongji.common.annotation.Anonymous;
import com.tongji.common.core.domain.R;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysMenu;
import com.tongji.system.entity.SysUser;
import com.tongji.web.dto.req.LoginReq;
import com.tongji.system.dto.req.RouterReq;
import com.tongji.security.model.LoginUser;
import com.tongji.system.service.SysMenuService;
import com.tongji.web.dto.resp.LoginInfoResp;
import com.tongji.web.dto.resp.LoginResp;
import com.tongji.web.service.SysLoginService;
import com.tongji.web.service.SysPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 */
@Tag(name = "登录验证", description = "处理用户登录、获取用户信息和路由信息的接口")
@RestController
public class SysLoginController {

    @Resource
    private SysLoginService sysLoginService;

    @Resource
    private SysMenuService sysMenuService;

    @Resource
    private SysPermissionService sysPermissionService;

    /**
     * 登录方法
     *
     * @param loginVo 登录信息
     * @return 结果
     */
    @Operation(summary = "管理员登录", description = "管理员登录验证")
    @ApiResponse(responseCode = "200", description = "登录成功", 
                 content = @Content(schema = @Schema(implementation = LoginResp.class)))
    @Anonymous
    @PostMapping("/login")
    public R<LoginResp> login(@RequestBody LoginReq loginVo) {
        // 生成令牌
        String token = sysLoginService.login(
                loginVo.getUsername(),
                loginVo.getPassword(),
                loginVo.getCode(),
                loginVo.getUuid()
        );
        
        // 创建LoginResp对象
        LoginResp loginResp = new LoginResp(token);
        
        // 如果需要返回更多信息，可以这样设置：
        // LoginUser loginUser = SecurityUtils.getLoginUser();
        // SysUser user = loginUser.getUser();
        // LoginResp loginResp = new LoginResp(token, user.getUserId(), user.getUsername());

        return R.successByData(loginResp);
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息、角色和权限")
    @ApiResponse(responseCode = "200", description = "成功获取用户信息", 
                 content = @Content(schema = @Schema(implementation = LoginInfoResp.class)))
    @GetMapping("getInfo")
    public R<LoginInfoResp> getInfo() {
        SysUser user = SecurityUtils.getLoginUser();
        // 角色集合
        Set<String> roles = sysPermissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = sysPermissionService.getMenuPermission(user);

        return R.successByData(new LoginInfoResp(user, roles, permissions));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @Operation(summary = "获取路由信息", description = "获取当前登录用户的菜单路由信息")
    @ApiResponse(responseCode = "200", description = "成功获取路由信息", 
                 content = @Content(schema = @Schema(implementation = RouterReq.class)))
    @GetMapping("getRouters")
    public R<List<RouterReq>> getRouters() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<SysMenu> menus = sysMenuService.selectMenuTreeByUserId(loginUser);
        return R.successByData(sysMenuService.buildMenus(menus));
    }
}
