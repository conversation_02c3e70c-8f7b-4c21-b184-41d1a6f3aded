package com.tongji.web.controller.monitor;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysLogininfor;
import com.tongji.system.dto.req.SysLogininforListReq;
import com.tongji.system.service.SysLogininforService;
import com.tongji.web.controller.BaseController;
import com.tongji.web.service.SysPasswordService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 */
@RestController
@RequestMapping("/monitor/logininfor")
@Tag(name = "系统访问记录", description = "系统访问记录管理接口")
public class SysLogininforController extends BaseController {

    @Resource
    private SysLogininforService sysLogininforService;

    @Resource
    private SysPasswordService sysPasswordService;

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:listPage')")
    @GetMapping("/listPage")
    @Operation(summary = "获取系统访问记录列表", description = "根据查询条件获取系统访问记录列表")
    public R<TablePage<SysLogininfor>> listPage(@Parameter(description = "查询参数") SysLogininforListReq listReq) {
        return R.successByData(sysLogininforService.selectLogininforList(listReq));
    }

    // TODO: excel
//    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysLogininfor logininfor)
//    {
//        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
//        ExcelUtil<SysLogininfor> util = new ExcelUtil<SysLogininfor>(SysLogininfor.class);
//        util.exportExcel(response, list, "登录日志");
//    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    @Operation(summary = "删除系统访问记录", description = "根据ID批量删除系统访问记录")
    public R<Object> remove(@Parameter(description = "记录ID数组") @PathVariable List<Long> infoIds) {
        return toAjax(sysLogininforService.deleteLogininforByIds(infoIds));
    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    @Operation(summary = "清空系统访问记录", description = "清空所有系统访问记录")
    public R<Object> clean() {
        sysLogininforService.cleanLogininfor();
        return success();
    }

    @PreAuthorize("@ss.hasPermi('monitor:logininfor:unlock')")
    @Log(title = "账户解锁", businessType = BusinessType.OTHER)
    @GetMapping("/unlock/{username}")
    @Operation(summary = "解锁用户账户", description = "根据用户名解锁被锁定的用户账户")
    public R<Object> unlock(@Parameter(description = "用户名") @PathVariable String username) {
        sysPasswordService.clearLoginRecordCache(username);
        return success();
    }
}
