package com.tongji.web.controller.monitor;

import com.tongji.common.annotation.Log;
import com.tongji.common.constant.CacheConstants;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.core.redis.RedisCache;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysUserOnline;
import com.tongji.security.model.LoginUser;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 在线用户监控
 */
@RestController
@RequestMapping("/monitor/online")
@Tag(name = "在线用户监控", description = "在线用户监控相关接口")
public class SysUserOnlineController extends BaseController {

    @Resource
    private RedisCache redisCache;

    /**
     * 设置在线用户信息
     *
     * @param user 用户信息
     * @return 在线用户
     */
    private SysUserOnline loginUserToUserOnline(LoginUser user) {
        if (StringUtils.isNull(user)) {
            return null;
        }
        SysUserOnline sysUserOnline = new SysUserOnline();
        sysUserOnline.setTokenId(user.getToken());
        sysUserOnline.setUsername(user.getUsername());
        sysUserOnline.setIpaddr(user.getIpaddr());
        sysUserOnline.setLoginLocation(user.getLoginLocation());
        sysUserOnline.setBrowser(user.getBrowser());
        sysUserOnline.setOs(user.getOs());
        sysUserOnline.setLoginTime(user.getLoginTime());
        if (StringUtils.isNotNull(user.getDept())) {
            sysUserOnline.setDeptName(user.getDept().getDeptName());
        }
        return sysUserOnline;
    }

    /**
     * 通过登录地址查询信息
     *
     * @param ipaddr 登录地址
     * @param user   用户信息
     * @return 在线用户信息
     */
    private SysUserOnline selectOnlineByIpaddr(String ipaddr, LoginUser user) {
        if (StringUtils.equals(ipaddr, user.getIpaddr())) {
            return loginUserToUserOnline(user);
        }
        return null;
    }

    /**
     * 通过用户名称查询信息
     *
     * @param username 用户名称
     * @param user     用户信息
     * @return 在线用户信息
     */
    private SysUserOnline selectOnlineByUsername(String username, LoginUser user) {
        if (StringUtils.equals(username, user.getUsername())) {
            return loginUserToUserOnline(user);
        }
        return null;
    }

    /**
     * 通过登录地址/用户名称查询信息
     *
     * @param ipaddr   登录地址
     * @param username 用户名称
     * @param user     用户信息
     * @return 在线用户信息
     */
    private SysUserOnline selectOnlineByInfo(String ipaddr, String username, LoginUser user) {
        if (StringUtils.equals(ipaddr, user.getIpaddr()) && StringUtils.equals(username, user.getUsername())) {
            return loginUserToUserOnline(user);
        }
        return null;
    }

    @PreAuthorize("@ss.hasPermi('monitor:online:list')")
    @GetMapping("/listPage")
    @Operation(summary = "获取在线用户列表", description = "根据IP地址和用户名查询在线用户列表")
    public R<TablePage<SysUserOnline>> listPage(
            @Parameter(description = "IP地址") @RequestParam(required = false) String ipaddr,
            @Parameter(description = "用户名") @RequestParam(required = false) String username
    ) {
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            LoginUser user = redisCache.getCacheObject(key);
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(username)) {
                userOnlineList.add(selectOnlineByInfo(ipaddr, username, user));
            } else if (StringUtils.isNotEmpty(ipaddr)) {
                userOnlineList.add(selectOnlineByIpaddr(ipaddr, user));
            } else if (StringUtils.isNotEmpty(username) && StringUtils.isNotNull(user)) {
                userOnlineList.add(selectOnlineByUsername(username, user));
            } else {
                userOnlineList.add(loginUserToUserOnline(user));
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        return R.successByData(getDataTable(userOnlineList));
    }

    /**
     * 强退用户
     */
    @PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
    @Log(title = "强制退出用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    @Operation(summary = "强制退出用户", description = "根据令牌ID强制用户退出登录")
    public R<Object> forceLogout(
            @Parameter(description = "令牌ID", required = true) @PathVariable String tokenId) {
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
        return success();
    }
}
