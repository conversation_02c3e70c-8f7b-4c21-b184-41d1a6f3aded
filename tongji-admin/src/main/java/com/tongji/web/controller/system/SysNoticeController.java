package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysNotice;
import com.tongji.system.dto.req.notice.SysNoticeListReq;
import com.tongji.system.dto.req.notice.SysNoticeAddReq;
import com.tongji.system.dto.req.notice.SysNoticeEditReq;
import com.tongji.system.service.SysNoticeService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 */
@RestController
@RequestMapping("/system/notice")
@Tag(name = "公告管理", description = "处理系统公告的CRUD操作")
public class SysNoticeController extends BaseController {

    @Resource
    private SysNoticeService sysNoticeService;

    /**
     * 获取通知公告列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/listPage")
    @Operation(summary = "获取通知公告列表", description = "根据查询条件分页获取通知公告列表")
    public R<TablePage<SysNotice>> listPage(@Parameter(description = "查询条件") SysNoticeListReq listReq) {
        return R.successByData(sysNoticeService.selectNoticeList(listReq));
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/{noticeId}")
    @Operation(summary = "获取公告详情", description = "根据公告ID获取公告的详细信息")
    public R<SysNotice> getInfo(@Parameter(description = "公告ID") @PathVariable Long noticeId) {
        return R.successByData(sysNoticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增通知公告", description = "创建新的通知公告")
    public R<Object> add(@Parameter(description = "新增公告信息") @Validated @RequestBody SysNoticeAddReq addReq) {
        return toAjax(sysNoticeService.insertNotice(addReq, getLoginUser()));
    }

    /**
     * 修改通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改通知公告", description = "更新现有的通知公告信息")
    public R<Object> edit(@Parameter(description = "修改公告信息") @Validated @RequestBody SysNoticeEditReq editReq) {
        return toAjax(sysNoticeService.updateNotice(editReq, getLoginUser()));
    }

    /**
     * 删除通知公告
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    @Operation(summary = "删除通知公告", description = "根据公告ID数组删除一个或多个通知公告")
    public R<Object> remove(@Parameter(description = "公告ID数组") @PathVariable List<Long> noticeIds) {
        return toAjax(sysNoticeService.deleteNoticeByIds(noticeIds));
    }
}
