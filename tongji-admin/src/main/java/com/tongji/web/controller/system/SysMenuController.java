package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysMenu;
import com.tongji.system.domain.TreeSelect;
import com.tongji.system.dto.req.menu.SysMenuListReq;
import com.tongji.system.dto.req.menu.SysMenuAddReq;
import com.tongji.system.dto.req.menu.SysMenuEditReq;
import com.tongji.system.service.SysMenuService;
import com.tongji.web.controller.BaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜单信息
 */
@RestController
@RequestMapping("/system/menu")
@Tag(name = "菜单管理", description = "处理系统菜单相关的操作")
public class SysMenuController extends BaseController {

    @Resource
    private SysMenuService sysMenuService;

    /**
     * 获取菜单列表
     */
    @PreAuthorize("@ss.hasPermi('system:menu:list')")
    @GetMapping("/list")
    @Operation(summary = "获取菜单列表", description = "根据查询条件获取菜单列表")
    @ApiResponse(responseCode = "200", description = "成功获取菜单列表")
    public R<List<SysMenu>> list(@Parameter(description = "菜单查询条件") SysMenuListReq listReq) {
        return R.successByData(sysMenuService.selectMenuList(listReq, getLoginUser()));
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:menu:query')")
    @GetMapping(value = "/{menuId}")
    @Operation(summary = "获取菜单详情", description = "根据菜单ID获取菜单详细信息")
    @ApiResponse(responseCode = "200", description = "成功获取菜单详情")
    public R<SysMenu> getInfo(@Parameter(description = "菜单ID") @PathVariable Long menuId) {
        return R.successByData(sysMenuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/treeSelect")
    @Operation(summary = "获取菜单树", description = "获取菜单的树形结构，用于下拉选择")
    @ApiResponse(responseCode = "200", description = "成功获取菜单树")
    public R<List<TreeSelect>> treeSelect(@Parameter(description = "菜单查询条件") SysMenuListReq listReq) {
        List<SysMenu> menus = sysMenuService.selectMenuList(listReq, getLoginUser());
        return R.successByData(sysMenuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping(value = "/roleMenuTreeSelect/{roleId}")
    @Operation(summary = "获取角色菜单树", description = "根据角色ID获取对应的菜单树和已选中的菜单")
    @ApiResponse(responseCode = "200", description = "成功获取角色菜单树")
    public R<Map<String, Object>> roleMenuTreeSelect(@Parameter(description = "角色ID") @PathVariable Long roleId) {
        List<SysMenu> menus = sysMenuService.selectMenuList(getLoginUser());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("checkedKeys", sysMenuService.selectMenuListByRoleId(roleId));
        resultMap.put("menus", sysMenuService.buildMenuTreeSelect(menus));
        return R.successByData(resultMap);
    }

    /**
     * 新增菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:add')")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增菜单", description = "新增一个菜单")
    @ApiResponse(responseCode = "200", description = "菜单新增成功")
    public R<Object> add(@Parameter(description = "新增菜单信息") @Validated @RequestBody SysMenuAddReq menuAddReq) {
        return toAjax(sysMenuService.insertMenu(menuAddReq, getLoginUser()));
    }

    /**
     * 修改菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:edit')")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改菜单", description = "修改现有菜单信息")
    @ApiResponse(responseCode = "200", description = "菜单修改成功")
    public R<Object> edit(@Parameter(description = "修改菜单信息") @Validated @RequestBody SysMenuEditReq menuEditReq) {
        return toAjax(sysMenuService.updateMenu(menuEditReq, getLoginUser()));
    }

    /**
     * 删除菜单
     */
    @PreAuthorize("@ss.hasPermi('system:menu:remove')")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    @Operation(summary = "删除菜单", description = "根据菜单ID删除菜单，不能删除有子菜单的菜单或已分配的菜单")
    @ApiResponse(responseCode = "200", description = "菜单删除成功或返回删除失败原因")
    public R<Object> remove(@Parameter(description = "菜单ID") @PathVariable Long menuId) {
        if (sysMenuService.hasChildByMenuId(menuId)) {
            return warnByMsg("存在子菜单,不允许删除");
        }
        if (sysMenuService.checkMenuExistRole(menuId)) {
            return warnByMsg("菜单已分配,不允许删除");
        }
        return toAjax(sysMenuService.deleteMenuById(menuId));
    }
}
