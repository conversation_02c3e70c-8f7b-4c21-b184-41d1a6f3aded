package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysDictType;
import com.tongji.system.dto.req.dict.SysDictTypeListReq;
import com.tongji.system.dto.req.dict.SysDictTypeAddReq;
import com.tongji.system.dto.req.dict.SysDictTypeEditReq;
import com.tongji.system.service.SysDictTypeService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据字典信息
 */
@Tag(name = "字典类型管理", description = "提供字典类型的增删改查功能")
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController extends BaseController {

    @Resource
    private SysDictTypeService sysDictTypeService;

    @Operation(summary = "获取字典类型列表", description = "根据查询条件分页获取字典类型列表")
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysDictType>> listPage(@Parameter(description = "字典类型查询条件") SysDictTypeListReq listReq) {
        return R.successByData(sysDictTypeService.selectDictTypeList(listReq));
    }

    // TODO: excel
//    @Log(title = "字典类型", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:dict:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysDictType dictType)
//    {
//        List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
//        ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
//        util.exportExcel(response, list, "字典类型");
//    }

    /**
     * 查询字典类型详细
     */
    @Operation(summary = "获取字典类型详情", description = "根据字典类型ID获取字典类型的详细信息")
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictId}")
    public R<SysDictType> getInfo(@Parameter(description = "字典类型ID") @PathVariable Long dictId) {
        return successByData(sysDictTypeService.selectDictTypeById(dictId));
    }

    /**
     * 新增字典类型
     */
    @Operation(summary = "新增字典类型", description = "新增一个字典类型")
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典类型", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@Validated @RequestBody SysDictTypeAddReq req) {
        return toAjax(sysDictTypeService.insertDictType(req, getLoginUser()));
    }

    /**
     * 修改字典类型
     */
    @Operation(summary = "修改字典类型", description = "根据字典类型ID修改字典类型信息")
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Object> edit(@Validated @RequestBody SysDictTypeEditReq req) {
        return toAjax(sysDictTypeService.updateDictType(req, getLoginUser()));
    }

    /**
     * 删除字典类型
     */
    @Operation(summary = "删除字典类型", description = "根据字典类型ID数组删除一个或多个字典类型")
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictIds}")
    public R<Object> remove(@Parameter(description = "字典类型ID数组") @PathVariable List<Long> dictIds) {
        sysDictTypeService.deleteDictTypeByIds(dictIds);
        return success();
    }

    /**
     * 刷新字典缓存
     */
    @Operation(summary = "刷新字典缓存", description = "刷新系统中的字典缓存")
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public R<Object> refreshCache() {
        sysDictTypeService.resetDictCache();
        return success();
    }

    /**
     * 获取字典选择框列表
     */
    @Operation(summary = "获取字典选择框列表", description = "获取所有可用于选择的字典类型列表")
    @GetMapping("/optionSelect")
    public R<List<SysDictType>> optionSelect() {
        List<SysDictType> dictTypes = sysDictTypeService.selectDictTypeAll();
        return successByData(dictTypes);
    }
}
