package com.tongji.web.controller.monitor;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.quartz.entity.SysJobLog;
import com.tongji.quartz.dto.req.SysJobLogListReq;
import com.tongji.quartz.service.SysJobLogService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度日志操作处理
 */
@RestController
@RequestMapping("/monitor/jobLog")
@Tag(name = "调度日志管理", description = "处理定时任务调度日志相关操作")
public class SysJobLogController extends BaseController {

    @Resource
    private SysJobLogService sysJobLogService;

    /**
     * 查询定时任务调度日志列表
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:list')")
    @GetMapping("/listPage")
    @Operation(summary = "查询定时任务调度日志列表", description = "根据查询条件获取定时任务调度日志列表")
    public R<TablePage<SysJobLog>> listPage(@Parameter(description = "查询条件") SysJobLogListReq listReq) {
        return R.successByData(sysJobLogService.selectJobLogList(listReq));
    }

    /**
     * 导出定时任务调度日志列表
     */
    // TODO: excel
//    @PreAuthorize("@ss.hasPermi('monitor:job:export')")
//    @Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysJobLog sysJobLog)
//    {
//        List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
//        ExcelUtil<SysJobLog> util = new ExcelUtil<SysJobLog>(SysJobLog.class);
//        util.exportExcel(response, list, "调度日志");
//    }

    /**
     * 根据调度编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:query')")
    @GetMapping(value = "/{jobLogId}")
    @Operation(summary = "获取调度日志详情", description = "根据调度编号获取定时任务调度日志的详细信息")
    public R<SysJobLog> getInfo(@Parameter(description = "调度日志ID") @PathVariable Long jobLogId) {
        return successByData(sysJobLogService.selectJobLogById(jobLogId));
    }

    /**
     * 删除定时任务调度日志
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @Log(title = "定时任务调度日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobLogIds}")
    @Operation(summary = "删除调度日志", description = "根据提供的ID数组删除定时任务调度日志")
    public R<Object> remove(@Parameter(description = "调度日志ID数组") @PathVariable List<Long> jobLogIds) {
        return toAjax(sysJobLogService.deleteJobLogByIds(jobLogIds));
    }

    /**
     * 清空定时任务调度日志
     */
    @PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @Log(title = "调度日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    @Operation(summary = "清空调度日志", description = "清空所有定时任务调度日志")
    public R<Object> clean() {
        sysJobLogService.cleanJobLog();
        return success();
    }
}
