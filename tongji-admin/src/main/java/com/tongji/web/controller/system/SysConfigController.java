package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.security.model.LoginUser;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysConfig;
import com.tongji.system.dto.req.config.SysConfigListReq;
import com.tongji.system.dto.req.config.SysConfigAddReq;
import com.tongji.system.dto.req.config.SysConfigEditReq;
import com.tongji.system.service.SysConfigService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置 信息操作处理
 */
@Tag(name = "参数配置接口", description = "用于管理系统参数配置的接口")
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController {

    @Resource
    private SysConfigService sysConfigService;

    /**
     * 获取参数配置列表
     */
    @Operation(summary = "获取参数配置列表", description = "根据查询条件获取参数配置列表，支持分页")
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysConfig>> listPage(@Parameter(description = "查询参数") SysConfigListReq listReq) {
        return R.successByData(sysConfigService.listConfigPage(listReq));
    }

    // TODO: excel
//    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:config:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysConfig config)
//    {
//        List<SysConfig> list = configService.selectConfigList(config);
//        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
//        util.exportExcel(response, list, "参数数据");
//    }

    /**
     * 根据参数编号获取详细信息
     */
    @Operation(summary = "获取参数详细信息", description = "根据参数ID获取参数的详细配置信息")
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public R<SysConfig> getInfo(@Parameter(description = "参数ID") @PathVariable Long configId) {
        return successByData(sysConfigService.queryConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @Operation(summary = "根据键名查询参数值", description = "通过参数的键名获取对应的参数值")
    @GetMapping(value = "/configKey/{configKey}")
    public R<String> getConfigKey(@Parameter(description = "参数键名") @PathVariable String configKey) {
        return successByData(sysConfigService.queryConfigByKey(configKey));
    }

    /**
     * 新增参数配置
     */
    @Operation(summary = "新增参数配置", description = "添加新的参数配置信息")
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@Parameter(description = "新增参数信息") @Validated @RequestBody SysConfigAddReq addReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysConfigService.addConfig(addReq, loginUser));
    }

    /**
     * 修改参数配置
     */
    @Operation(summary = "修改参数配置", description = "更新已有的参数配置信息")
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Object> edit(@Parameter(description = "修改参数信息") @Validated @RequestBody SysConfigEditReq editReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysConfigService.updateConfig(editReq, loginUser));
    }

    /**
     * 删除参数配置
     */
    @Operation(summary = "删除参数配置", description = "根据参数ID数组批量删除参数配置")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public R<Object> remove(@Parameter(description = "参数ID数组") @PathVariable List<Long> configIds) {
        sysConfigService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @Operation(summary = "刷新参数缓存", description = "清除并重新加载系统参数缓存")
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public R<Object> refreshCache() {
        sysConfigService.resetConfigCache();
        return success();
    }
}
