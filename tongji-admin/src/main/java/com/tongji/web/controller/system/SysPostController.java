package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysPost;
import com.tongji.system.dto.req.post.SysPostAddReq;
import com.tongji.system.dto.req.post.SysPostListReq;
import com.tongji.system.dto.req.post.SysPostEditReq;
import com.tongji.system.service.SysPostService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 岗位信息操作处理
 */
@RestController
@RequestMapping("/system/post")
@Tag(name = "岗位管理", description = "处理岗位相关的CRUD操作")
public class SysPostController extends BaseController {

    @Resource
    private SysPostService sysPostService;

    /**
     * 获取岗位列表
     */
    @PreAuthorize("@ss.hasPermi('system:post:list')")
    @GetMapping("/listPage")
    @Operation(summary = "获取岗位列表", description = "根据查询条件获取岗位列表，支持分页")
    public R<TablePage<SysPost>> listPage(@Parameter(description = "岗位查询条件") SysPostListReq listReq) {
        return R.successByData(sysPostService.selectPostList(listReq));
    }

    // TODO: excel
//    @Log(title = "岗位管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:post:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysPost post)
//    {
//        List<SysPost> list = postService.selectPostList(post);
//        ExcelUtil<SysPost> util = new ExcelUtil<SysPost>(SysPost.class);
//        util.exportExcel(response, list, "岗位数据");
//    }

    /**
     * 根据岗位编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:post:query')")
    @GetMapping(value = "/{postId}")
    @Operation(summary = "获取岗位详细信息", description = "根据岗位ID获取岗位的详细信息")
    public R<SysPost> getInfo(@Parameter(description = "岗位ID") @PathVariable Long postId) {
        return successByData(sysPostService.selectPostById(postId));
    }

    /**
     * 新增岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:add')")
    @Log(title = "岗位管理", businessType = BusinessType.INSERT)
    @PostMapping
    @Operation(summary = "新增岗位", description = "新增一个岗位，需要提供岗位的详细信息")
    public R<Object> add(@Parameter(description = "新增岗位信息") @Validated @RequestBody SysPostAddReq postAddReq) {
        return toAjax(sysPostService.insertPost(postAddReq, getLoginUser()));
    }

    /**
     * 修改岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:edit')")
    @Log(title = "岗位管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @Operation(summary = "修改岗位", description = "修改现有岗位的信息")
    public R<Object> edit(@Parameter(description = "修改岗位信息") @Validated @RequestBody SysPostEditReq postEditReq) {
        return toAjax(sysPostService.updatePost(postEditReq, getLoginUser()));
    }

    /**
     * 删除岗位
     */
    @PreAuthorize("@ss.hasPermi('system:post:remove')")
    @Log(title = "岗位管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    @Operation(summary = "删除岗位", description = "根据岗位ID删除一个或多个岗位")
    public R<Object> remove(@Parameter(description = "岗位ID数组") @PathVariable List<Long> postIds) {
        return toAjax(sysPostService.deletePostByIds(postIds));
    }

    /**
     * 获取岗位选择框列表
     */
    @GetMapping("/optionSelect")
    @Operation(summary = "获取岗位选择列表", description = "获取所有可选的岗位列表，用于下拉选择框")
    public R<List<SysPost>> optionSelect() {
        List<SysPost> posts = sysPostService.selectPostAll();
        return successByData(posts);
    }
}
