package com.tongji.web.controller;

import com.github.pagehelper.PageInfo;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.DateUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.security.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;

/**
 * web层通用数据处理
 */
public class BaseController {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

//    /**
//     * 设置请求分页数据
//     */
//    protected void startPage() {
//        PageUtils.startPage();
//    }
//
//    /**
//     * 设置请求排序数据
//     */
//    protected void startOrderBy() {
//        PageDomain pageDomain = TableSupport.buildPageRequest();
//        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
//            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
//            PageHelper.orderBy(orderBy);
//        }
//    }
//
//    /**
//     * 清理分页的线程变量
//     */
//    protected void clearPage() {
//        PageUtils.clearPage();
//    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected <T> TablePage<T> getDataTable(List<T> list) {
        TablePage<T> rspData = new TablePage<>();
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 返回成功
     */
    public <T> R<T> success() {
        return R.success();
    }

    /**
     * 返回失败消息
     */
    public <T> R<T> error() {
        return R.error();
    }

    /**
     * 返回成功消息
     */
    public <T> R<T> successByMsg(String message) {
        return R.successByMsg(message);
    }

    /**
     * 返回成功消息
     */
    public <T> R<T> successByData(T data) {
        return R.successByData(data);
    }

    /**
     * 返回失败消息
     */
    public <T> R<T> errorByMsg(String message) {
        return R.errorByMsg(message);
    }

    /**
     * 返回警告消息
     */
    public <T> R<T> warnByMsg(String message) {
        return R.warnByMsg(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected <T> R<T> toAjax(int rows) {
        return rows > 0 ? R.success() : R.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected <T> R<T> toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected <T> R<T> delete(int rows) {
        return rows > 0 ? R.successByMsg("删除成功") : R.errorByMsg("删除失败");
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }

    /**
     * 获取用户缓存信息
     */
    public LoginUser getLoginUser() {
        return SecurityUtils.getLoginUser();
    }

    /**
     * 获取登录用户id
     */
    public Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取登录部门id
     */
    public Long getDeptId() {
        return getLoginUser().getDeptId();
    }

    /**
     * 获取登录用户名
     */
    public String getUsername() {
        return getLoginUser().getUsername();
    }
}
