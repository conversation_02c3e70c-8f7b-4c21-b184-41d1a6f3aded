package com.tongji.web.controller.common;

import com.tongji.common.annotation.Anonymous;
import com.google.code.kaptcha.Producer;
import com.tongji.common.config.AppConfig;
import com.tongji.common.constant.CacheConstants;
import com.tongji.common.constant.Constants;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.redis.RedisCache;
import com.tongji.common.utils.sign.Base64;
import com.tongji.common.utils.uuid.IdUtils;
import com.tongji.system.service.SysConfigService;
import com.tongji.web.dto.resp.CaptchaImageResp;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 验证码操作处理
 */
@Tag(name = "验证码")
@RestController
public class CaptchaController {

    @Resource(name = "captchaProducer")
    private Producer captchaProducer;

    @Resource(name = "captchaProducerMath")
    private Producer captchaProducerMath;

    @Resource
    private RedisCache redisCache;

    @Resource
    private SysConfigService configService;

    /**
     * 生成验证码
     */
    @Anonymous
    @Operation(summary = "生成验证码（不兼容）", description = "生成验证码")
    @GetMapping("/captchaImage")
    public R<CaptchaImageResp> getCode(HttpServletResponse response) {
        CaptchaImageResp captchaResp = new CaptchaImageResp();
        boolean captchaEnabled = configService.queryCaptchaEnabled();
        captchaResp.setCaptchaEnabled(captchaEnabled);
        if (!captchaEnabled) {
            return R.successByData(captchaResp);
        }

        // 保存验证码信息
        String uuid = IdUtils.simpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + uuid;

        String capStr, code = null;
        BufferedImage image = null;

        // 生成验证码
        String captchaType = AppConfig.getCaptchaType();
        if ("math".equals(captchaType)) {
            String capText = captchaProducerMath.createText();
            capStr = capText.substring(0, capText.lastIndexOf("@"));
            code = capText.substring(capText.lastIndexOf("@") + 1);
            image = captchaProducerMath.createImage(capStr);
        } else if ("char".equals(captchaType)) {
            capStr = code = captchaProducer.createText();
            image = captchaProducer.createImage(capStr);
        }

        redisCache.setCacheObject(verifyKey, code, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            assert image != null;
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            return R.errorByMsg(e.getMessage());
        }

        captchaResp.setUuid(uuid);
        captchaResp.setImg(Base64.encode(os.toByteArray()));
        return R.successByData(captchaResp);
    }
}
