package com.tongji.web.controller.logic;

import com.tongji.common.core.domain.R;
import com.tongji.common.core.dto.IdReq;
import com.tongji.common.core.page.TablePage;
import com.tongji.logic.dto.req.task.TaskAddReq;
import com.tongji.logic.dto.req.task.TaskListReq;
import com.tongji.logic.entity.task.TTask;
import com.tongji.logic.service.TaskService;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "任务管理")
@RestController
@RequestMapping("/logic/task")
public class TaskController {

    @Resource
    private TaskService taskService;

    @Operation(summary = "查询任务列表")
    @PreAuthorize("@ss.hasPermi('logic:task:listPage')")
    @GetMapping("/listPage")
    public R<TablePage<TTask>> listPage(TaskListReq req) {
        return R.successByData(taskService.queryPage(req));
    }

    @Operation(summary = "获取任务详情")
    @PreAuthorize("@ss.hasPermi('logic:task:detail')")
    @GetMapping(value = "/{taskId}")
    public R<TTask> getInfo(@Parameter(description = "任务ID") @PathVariable Long taskId) {
        return R.successByData(taskService.queryDetail(taskId));
    }

    @Operation(summary = "添加任务")
    @PreAuthorize("@ss.hasPermi('logic:task:add')")
    @PostMapping("/add")
    public R<Object> add(@RequestBody TaskAddReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        taskService.addTask(req, user);
        return R.success();
    }

    @Operation(summary = "删除任务")
    @PreAuthorize("@ss.hasPermi('logic:task:delete')")
    @PostMapping("/delete")
    public R<Object> delete(@RequestBody IdReq req) {
        SysUser user = SecurityUtils.getLoginUser();
        taskService.deleteTask(req.getId(), user);
        return R.success();
    }
}
