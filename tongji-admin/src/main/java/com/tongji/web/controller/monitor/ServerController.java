package com.tongji.web.controller.monitor;

import com.tongji.common.core.domain.R;
import com.tongji.system.domain.Server;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务器监控
 */
@Tag(name = "服务器监控", description = "服务器监控相关接口")
@RestController
@RequestMapping("/monitor/server")
public class ServerController {

    @Operation(summary = "获取服务器信息", description = "获取服务器的详细信息")
    @PreAuthorize("@ss.hasPermi('monitor:server:list')")
    @GetMapping()
    public R<Server> getInfo() throws Exception {
        Server server = new Server();
        server.copyTo();
        return R<PERSON>success<PERSON>y<PERSON>(server);
    }
}
