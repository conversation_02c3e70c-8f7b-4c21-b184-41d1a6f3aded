package com.tongji.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.security.model.LoginUser;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysUser;
import com.tongji.system.domain.TreeSelect;
import com.tongji.system.dto.req.dept.SysDeptListReq;
import com.tongji.system.dto.req.user.SysUserAddReq;
import com.tongji.system.dto.req.user.SysUserEditReq;
import com.tongji.system.dto.req.user.SysUserListReq;
import com.tongji.system.dto.resp.user.UserAuthRoleResp;
import com.tongji.system.dto.resp.user.UserInfoResp;
import com.tongji.system.service.SysDeptService;
import com.tongji.system.service.SysRoleService;
import com.tongji.system.service.SysUserService;
import com.tongji.web.controller.BaseController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户信息
 */
@RestController
@RequestMapping("/system/user")
@Tag(name = "用户管理", description = "用户相关接口，包括用户的增删改查、授权等操作")
public class SysUserController extends BaseController {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysDeptService sysDeptService;

    /**
     * 获取用户列表
     */
    @Operation(summary = "获取用户列表", description = "根据查询条件分页获取用户列表")
    @ApiResponse(responseCode = "200", description = "成功获取用户列表",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = TablePage.class)))
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysUser>> listPage(@Parameter(description = "用户查询条件") SysUserListReq listReq) {
        return R.successByData(sysUserService.selectUserListPage(listReq));
    }

    // TODO: excel
//    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:user:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysUser user)
//    {
//        List<SysUser> list = userService.selectUserList(user);
//        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//        util.exportExcel(response, list, "用户数据");
//    }

    // TODO: excel
//    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
//    @PreAuthorize("@ss.hasPermi('system:user:import')")
//    @PostMapping("/importData")
//    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
//    {
//        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//        List<SysUser> userList = util.importExcel(file.getInputStream());
//        String operName = getUsername();
//        String message = userService.importUser(userList, updateSupport, operName);
//        return success(message);
//    }

    // TODO: excel
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response)
//    {
//        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
//        util.importTemplateExcel(response, "用户数据");
//    }

    /**
     * 根据用户编号获取详细信息
     */
    @Operation(summary = "获取用户详细信息", description = "根据用户ID获取用户的详细信息，包括角色和岗位")
    @ApiResponse(responseCode = "200", description = "成功获取用户详细信息",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = UserInfoResp.class)))
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public R<UserInfoResp> getInfo(
            @Parameter(description = "用户ID") @PathVariable(required = false) Long userId
    ) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return R.successByData(sysUserService.selectUserById(userId, loginUser));
    }

    /**
     * 新增用户
     */
    @Operation(summary = "新增用户", description = "新增一个用户，包括用户基本信息、角色和岗位")
    @ApiResponse(responseCode = "200", description = "成功新增用户",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@Parameter(description = "新增用户信息") @Validated @RequestBody SysUserAddReq userAddReq) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        userAddReq.setPassword(SecurityUtils.encryptPassword(userAddReq.getPassword()));
        return toAjax(sysUserService.insertUser(userAddReq, loginUser));
    }

    /**
     * 修改用户
     */
    @Operation(summary = "修改用户", description = "修改用户信息，包括用户基本信息、角色和岗位")
    @ApiResponse(responseCode = "200", description = "成功修改用户",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Object> edit(@Parameter(description = "修改用户信息") @Validated @RequestBody SysUserEditReq userEditReq) {
        System.out.println("userEditReq = " + JSON.toJSONString(userEditReq));
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysUserService.updateUser(userEditReq, loginUser));
    }

    /**
     * 删除用户
     */
    @Operation(summary = "删除用户", description = "根据用户ID删除用户，支持批量删除")
    @ApiResponse(responseCode = "200", description = "成功删除用户",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Object> remove(@Parameter(description = "用户ID列表") @PathVariable List<Long> userIds) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (userIds.contains(loginUser.getUserId())) {
            return R.errorByMsg("当前用户不能删除");
        }

        return toAjax(sysUserService.deleteUserByIds(userIds, loginUser));
    }

    /**
     * 重置密码
     */
    @Operation(summary = "重置用户密码", description = "重置指定用户的密码")
    @ApiResponse(responseCode = "200", description = "成功重置用户密码",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Object> resetPwd(@Parameter(description = "用户信息") @RequestBody SysUser user) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysUserService.checkUserAllowed(user.getUserId());
        sysUserService.checkUserDataScope(user.getUserId(), loginUser);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(sysUserService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @Operation(summary = "修改用户状态", description = "修改用户的状态（正常/停用）")
    @ApiResponse(responseCode = "200", description = "成功修改用户状态",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Object> changeStatus(@Parameter(description = "用户信息") @RequestBody SysUser user) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysUserService.checkUserAllowed(user.getUserId());
        sysUserService.checkUserDataScope(user.getUserId(), loginUser);
        user.setUpdateBy(getUsername());
        return toAjax(sysUserService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @Operation(summary = "获取用户授权角色", description = "根据用户ID获取用户的授权角色信息")
    @ApiResponse(responseCode = "200", description = "成功获取用户授权角色信息",
            content = @Content(mediaType = "application/json", 
            schema = @Schema(implementation = UserAuthRoleResp.class)))
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public R<UserAuthRoleResp> authRole(@Parameter(description = "用户ID") @PathVariable Long userId) {
        return R.successByData(sysUserService.getUserAuthRole(userId));
    }

    /**
     * 用户授权角色
     */
    @Operation(summary = "用户授权角色", description = "为指定用户授予角色权限")
    @ApiResponse(responseCode = "200", description = "成功授权用户角色",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = R.class)))
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public R<Object> insertAuthRole(@Parameter(description = "用户ID") @RequestParam Long userId, 
                                    @Parameter(description = "角色ID列表") @RequestParam(defaultValue = "") List<Long> roleIds) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysUserService.checkUserDataScope(userId, loginUser);
        sysRoleService.checkRoleDataScope(roleIds, loginUser);
        sysUserService.insertUserAuth(userId, roleIds);
        return R.success();
    }

    /**
     * 获取部门树列表
     */
    @Operation(summary = "获取部门树列表", description = "获取部门的树形结构列表")
    @ApiResponse(responseCode = "200", description = "成功获取部门树列表",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = TreeSelect.class)))
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/deptTree")
    public R<List<TreeSelect>> deptTree(@Parameter(description = "部门查询条件") SysDeptListReq listReq) {
        return R.successByData(sysDeptService.listDeptTree(listReq));
    }
}
