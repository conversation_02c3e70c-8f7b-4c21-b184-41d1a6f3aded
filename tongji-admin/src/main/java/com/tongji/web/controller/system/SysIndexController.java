package com.tongji.web.controller.system;

import com.tongji.common.config.AppConfig;
import com.tongji.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页
 */
@RestController
@RequestMapping("/")
@Tag(name = "系统首页控制器", description = "处理系统首页相关的请求")
public class SysIndexController {
    /**
     * 系统基础配置
     */
    @Resource
    private AppConfig appConfig;

    /**
     * 访问首页，提示语
     */
    @GetMapping
    @Operation(summary = "获取首页欢迎信息", description = "返回一个包含系统名称和版本号的欢迎信息。" )
    public String index() {
        return StringUtils.format("欢迎使用{}后台管理框架，当前版本：v{}，请通过前端地址访问。", appConfig.getName(), appConfig.getVersion());
    }
}
