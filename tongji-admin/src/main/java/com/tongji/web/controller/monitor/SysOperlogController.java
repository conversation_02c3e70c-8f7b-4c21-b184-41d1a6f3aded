package com.tongji.web.controller.monitor;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.system.entity.SysOperLog;
import com.tongji.system.service.SysOperLogService;
import com.tongji.web.controller.BaseController;
import com.tongji.system.dto.req.SysOperLogListReq;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志记录
 */
@RestController
@RequestMapping("/monitor/operlog")
@Tag(name = "操作日志管理", description = "操作日志记录相关接口")
public class SysOperlogController extends BaseController {

    @Resource
    private SysOperLogService sysOperLogService;

    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/listPage")
    @Operation(summary = "查询操作日志列表", description = "根据条件查询操作日志列表")
    @ApiResponse(responseCode = "200", description = "成功获取操作日志列表")
    public R<TablePage<SysOperLog>> listPage(@Parameter(description = "查询参数") SysOperLogListReq listReq) {
        return R.successByData(sysOperLogService.selectOperLogList(listReq));
    }

    // TODO: excel
//    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysOperLog operLog)
//    {
//        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
//        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
//        util.exportExcel(response, list, "操作日志");
//    }

    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/{operIds}")
    @Operation(summary = "删除操作日志", description = "根据操作日志ID删除操作日志")
    @ApiResponse(responseCode = "200", description = "成功删除操作日志")
    public R<Object> remove(@Parameter(description = "操作日志ID数组") @PathVariable List<Long> operIds) {
        return toAjax(sysOperLogService.deleteOperLogByIds(operIds));
    }

    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/clean")
    @Operation(summary = "清空操作日志", description = "清空所有操作日志")
    @ApiResponse(responseCode = "200", description = "成功清空操作日志")
    public R<Object> clean() {
        sysOperLogService.cleanOperLog();
        return success();
    }
}
