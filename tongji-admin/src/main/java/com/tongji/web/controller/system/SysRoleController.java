package com.tongji.web.controller.system;

import java.util.List;

import com.tongji.system.service.SysDeptService;
import com.tongji.system.service.SysRoleService;
import com.tongji.system.service.SysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.utils.StringUtils;
import com.tongji.security.model.LoginUser;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysRole;
import com.tongji.system.entity.SysUser;
import com.tongji.system.entity.SysUserRole;
import com.tongji.system.dto.req.role.SysRoleAddReq;
import com.tongji.system.dto.req.role.SysRoleDataScopeReq;
import com.tongji.system.dto.req.role.SysRoleEditReq;
import com.tongji.system.dto.req.role.SysRoleListReq;
import com.tongji.system.dto.req.role.SysRoleStatusChangeReq;
import com.tongji.system.dto.req.user.SysUserAllocatedRoleQueryReq;
import com.tongji.system.dto.resp.dept.DeptTreeCheckedResp;
import com.tongji.web.controller.BaseController;
import com.tongji.web.service.SysPermissionService;
import com.tongji.web.service.TokenService;

import jakarta.annotation.Resource;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 角色信息
 */
@RestController
@RequestMapping("/system/role")
@Tag(name = "角色管理", description = "角色相关接口，包括角色的增删改查、授权等操作")
public class SysRoleController extends BaseController {

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private TokenService tokenService;

    @Resource
    private SysPermissionService sysPermissionService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysDeptService sysDeptService;

    @Operation(summary = "获取角色列表", description = "分页查询角色列表，支持多条件筛选")
    @ApiResponse(responseCode = "200", description = "成功获取角色列表",
            content = @Content(schema = @Schema(implementation = TablePage.class)))
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysRole>> listPage(SysRoleListReq listReq) {
        return R.successByData(sysRoleService.selectRoleListPage(listReq));
    }

    // TODO: excel
//    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:role:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysRole role)
//    {
//        List<SysRole> list = roleService.selectRoleList(role);
//        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
//        util.exportExcel(response, list, "角色数据");
//    }

    /**
     * 根据角色编号获取详细信息
     */
    @Operation(summary = "获取角色详细信息", description = "根据角色ID获取角色的详细信息")
    @ApiResponse(responseCode = "200", description = "成功获取角色详细信息",
            content = @Content(schema = @Schema(implementation = SysRole.class)))
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/{roleId}")
    public R<SysRole> getInfo(@Parameter(description = "角色ID", required = true) @PathVariable Long roleId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysRoleService.checkRoleDataScope(List.of(roleId), loginUser);
        return R.successByData(sysRoleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @Operation(summary = "新增角色", description = "新增一个角色，包括角色基本信息、菜单权限、数据权限等")
    @ApiResponse(responseCode = "200", description = "成功新增角色")
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@Parameter(description = "新增角色信息", required = true) @Validated @RequestBody SysRoleAddReq req) {
        return toAjax(sysRoleService.insertRole(req, getLoginUser()));
    }

    /**
     * 修改保存角色
     */
    @Operation(summary = "修改角色", description = "修改角色信息，包括角色基本信息、菜单权限、数据权限等")
    @ApiResponse(responseCode = "200", description = "成功修改角色")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Object> edit(@Parameter(description = "修改角色信息", required = true) @Validated @RequestBody SysRoleEditReq req) {
        if (sysRoleService.updateRole(req, getLoginUser()) > 0) {
            // 更新缓存用户权限
            LoginUser loginUser = getLoginUser();
            if (StringUtils.isNotNull(loginUser) && !loginUser.isAdmin()) {
                BeanUtils.copyProperties(sysUserService.selectUserByUsername(loginUser.getUsername()), loginUser);
                loginUser.setPermissions(sysPermissionService.getMenuPermission(loginUser));
                tokenService.setLoginUser(loginUser);
            }
            return success();
        }
        return errorByMsg("修改角色'" + req.getRoleName() + "'失败，请联系管理员");
    }

    /**
     * 修改保存数据权限
     */
    @Operation(summary = "修改角色数据权限", description = "修改角色的数据权限范围")
    @ApiResponse(responseCode = "200", description = "成功修改角色数据权限")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public R<Object> dataScope(@Parameter(description = "角色数据权限信息", required = true) @RequestBody SysRoleDataScopeReq req) {
        LoginUser loginUser = getLoginUser();
        sysRoleService.authDataScope(req, loginUser);

        loginUser.setRoles(loginUser.getRoles().stream().peek(r -> {
            if (r.getRoleId().equals(req.getRoleId())) {
                r.setDataScope(req.getDataScope());
            }
        }).toList());
        tokenService.setLoginUser(loginUser);

        return R.success();
    }

    /**
     * 状态修改
     */
    @Operation(summary = "修改角色状态", description = "修改角色的状态（正常/停用）")
    @ApiResponse(responseCode = "200", description = "成功修改角色状态")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Object> changeStatus(@Parameter(description = "角色状态信息", required = true) @RequestBody SysRoleStatusChangeReq req) {
        return toAjax(sysRoleService.updateRoleStatus(req, getLoginUser()));
    }

    /**
     * 删除角色
     */
    @Operation(summary = "删除角色", description = "根据角色ID删除角色，支持批量删除")
    @ApiResponse(responseCode = "200", description = "成功删除角色")
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public R<Object> remove(@Parameter(description = "角色ID列表", required = true) @PathVariable List<Long> roleIds) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysRoleService.deleteRoleByIds(roleIds, loginUser));
    }

    /**
     * 获取角色选择框列表
     */
    @Operation(summary = "获取角色选择框列表", description = "获取所有可选择的角色列表")
    @ApiResponse(responseCode = "200", description = "成功获取角色选择框列表",
            content = @Content(schema = @Schema(implementation = SysRole.class)))
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping("/optionSelect")
    public R<List<SysRole>> optionSelect() {
        return R.successByData(sysRoleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    @Operation(summary = "查询已分配用户角色列表", description = "分页查询已分配指定角色的用户列表")
    @ApiResponse(responseCode = "200", description = "成功获取已分配用户角色列表",
            content = @Content(schema = @Schema(implementation = TablePage.class)))
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/allocatedListPage")
    public R<TablePage<SysUser>> allocatedListPage(SysUserAllocatedRoleQueryReq req) {
        return R.successByData(sysUserService.selectAllocatedList(req));
    }

    /**
     * 查询未分配用户角色列表
     */
    @Operation(summary = "查询未分配用户角色列表", description = "分页查询未分配指定角色的用户列表")
    @ApiResponse(responseCode = "200", description = "成功获取未分配用户角色列表",
            content = @Content(schema = @Schema(implementation = TablePage.class)))
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/unallocatedListPage")
    public R<TablePage<SysUser>> unallocatedListPage(SysUserAllocatedRoleQueryReq req) {
        return R.successByData(sysUserService.selectUnallocatedList(req));
    }

    /**
     * 取消授权用户
     */
    @Operation(summary = "取消授权用户", description = "取消指定用户的角色授权")
    @ApiResponse(responseCode = "200", description = "成功取消授权用户")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public R<Object> cancelAuthUser(@Parameter(description = "用户角色信息", required = true) @RequestBody SysUserRole userRole) {
        return toAjax(sysRoleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    @Operation(summary = "批量取消授权用户", description = "批量取消指定用户的角色授权")
    @ApiResponse(responseCode = "200", description = "成功批量取消授权用户")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public R<Object> cancelAuthUserAll(
            @Parameter(description = "角色ID", required = true) Long roleId,
            @Parameter(description = "用户ID列表", required = true) List<Long> userIds) {
        return toAjax(sysRoleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权
     */
    @Operation(summary = "批量选择用户授权", description = "批量为指定用户授予角色权限")
    @ApiResponse(responseCode = "200", description = "成功批量选择用户授权")
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public R<Object> selectAuthUserAll(
            @Parameter(description = "角色ID", required = true) Long roleId,
            @Parameter(description = "用户ID列表", required = true) List<Long> userIds) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        sysRoleService.checkRoleDataScope(List.of(roleId), loginUser);
        return toAjax(sysRoleService.insertAuthUsers(roleId, userIds));
    }

    /**
     * 获取对应角色部门树列表
     */
    @Operation(summary = "获取角色对应的部门树", description = "根据角色ID获取部门树和选中的部门")
    @ApiResponse(responseCode = "200", description = "成功获取部门树和选中的部门",
            content = @Content(schema = @Schema(implementation = DeptTreeCheckedResp.class)))
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/deptTree/{roleId}")
    public R<DeptTreeCheckedResp> deptTree(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long roleId) {
        DeptTreeCheckedResp result = sysDeptService.queryDeptTreeAndCheckedKeys(roleId);
        return R.successByData(result);
    }
}
