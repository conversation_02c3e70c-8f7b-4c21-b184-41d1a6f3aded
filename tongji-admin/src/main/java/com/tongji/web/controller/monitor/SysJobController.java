package com.tongji.web.controller.monitor;

import com.tongji.common.annotation.Log;
import com.tongji.common.constant.Constants;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.dto.IdReq;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.exception.job.TaskException;
import com.tongji.common.utils.StringUtils;
import com.tongji.quartz.entity.SysJob;
import com.tongji.quartz.dto.req.JobAddReq;
import com.tongji.quartz.dto.req.JobEditReq;
import com.tongji.quartz.dto.req.JobStatusChangeReq;
import com.tongji.quartz.dto.req.JobQueryReq;
import com.tongji.quartz.service.SysJobService;
import com.tongji.quartz.util.CronUtils;
import com.tongji.quartz.util.ScheduleUtils;
import com.tongji.security.model.LoginUser;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

import org.quartz.SchedulerException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度任务信息操作处理
 */
@Tag(name = "调度任务管理", description = "调度任务信息操作处理")
@RestController
@RequestMapping("/monitor/job")
public class SysJobController extends BaseController {

    @Resource
    private SysJobService sysJobService;

    /**
     * 查询定时任务列表
     */
    @Operation(summary = "查询定时任务列表", description = "获取所有定时任务的列表")
    @PreAuthorize("@ss.hasPermi('monitor:job:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysJob>> listPage(JobQueryReq jobQueryReq) {
        return R.successByData(sysJobService.selectJobList(jobQueryReq));
    }

    /**
     * 导出定时任务列表
     */
    // TODO: excel
//    @PreAuthorize("@ss.hasPermi('monitor:job:export')")
//    @Log(title = "定时任务", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysJob sysJob)
//    {
//        List<SysJob> list = jobService.selectJobList(sysJob);
//        ExcelUtil<SysJob> util = new ExcelUtil<SysJob>(SysJob.class);
//        util.exportExcel(response, list, "定时任务");
//    }

    /**
     * 获取定时任务详细信息
     */
    @Operation(summary = "获取定时任务详细信息", description = "根据任务ID获取定时任务的详细信息")
    @PreAuthorize("@ss.hasPermi('monitor:job:query')")
    @GetMapping(value = "/{jobId}")
    public R<SysJob> getInfo(@Parameter(description = "任务ID") @PathVariable Long jobId) {
        return successByData(sysJobService.selectJobById(jobId));
    }

    /**
     * 新增定时任务
     */
    @Operation(summary = "新增定时任务", description = "添加新的定时任务")
    @PreAuthorize("@ss.hasPermi('monitor:job:add')")
    @Log(title = "定时任务", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@RequestBody JobAddReq jobAddReq) throws SchedulerException, TaskException {
        if (!CronUtils.isValid(jobAddReq.getCronExpression())) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(jobAddReq.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobAddReq.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobAddReq.getInvokeTarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobAddReq.getInvokeTarget(), Constants.JOB_ERROR_STR)) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，目标字符串存在违规");
        } else if (!ScheduleUtils.whiteList(jobAddReq.getInvokeTarget())) {
            return errorByMsg("新增任务'" + jobAddReq.getJobName() + "'失败，目标字符串不在白名单内");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysJobService.insertJob(jobAddReq, loginUser));
    }

    /**
     * 修改定时任务
     */
    @Operation(summary = "修改定时任务", description = "更新现有的定时任务")
    @PreAuthorize("@ss.hasPermi('monitor:job:edit')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Object> edit(@RequestBody JobEditReq jobEditReq) throws SchedulerException, TaskException {
        if (!CronUtils.isValid(jobEditReq.getCronExpression())) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(jobEditReq.getInvokeTarget(), Constants.LOOKUP_RMI)) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobEditReq.getInvokeTarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobEditReq.getInvokeTarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(jobEditReq.getInvokeTarget(), Constants.JOB_ERROR_STR)) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，目标字符串存在违规");
        } else if (!ScheduleUtils.whiteList(jobEditReq.getInvokeTarget())) {
            return errorByMsg("修改任务'" + jobEditReq.getJobName() + "'失败，目标字符串不在白名单内");
        }

        LoginUser loginUser = SecurityUtils.getLoginUser();
        return toAjax(sysJobService.updateJob(jobEditReq, loginUser));
    }

    /**
     * 定时任务状态修改
     */
    @Operation(summary = "修改定时任务状态", description = "更新定时任务的状态（启用/禁用）")
    @PreAuthorize("@ss.hasPermi('monitor:job:changeStatus')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Object> changeStatus(@RequestBody JobStatusChangeReq req) throws SchedulerException {
        SysJob job = sysJobService.selectJobById(req.getJobId());
        if (job == null) {
            return errorByMsg("任务不存在");
        }
        job.setStatus(req.getStatus());
        return toAjax(sysJobService.changeStatus(job));
    }

    /**
     * 定时任务立即执行一次
     */
    @Operation(summary = "立即执行定时任务", description = "立即执行一次指定的定时任务")
    @PreAuthorize("@ss.hasPermi('monitor:job:changeStatus')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping("/run")
    public R<Object> run(@RequestBody IdReq req) throws SchedulerException {
        boolean result = sysJobService.run(req.getId());
        return result ? success() : errorByMsg("任务不存在或已过期！");
    }

    /**
     * 删除定时任务
     */
    @Operation(summary = "删除定时任务", description = "删除指定的定时任务")
    @PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @Log(title = "定时任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public R<Object> remove(
        @Parameter(description = "任务ID数组") @PathVariable List<Long> jobIds
    ) throws SchedulerException, TaskException {
        sysJobService.deleteJobByIds(jobIds);
        return success();
    }
    
}