package com.tongji.web.controller.system;

import com.tongji.common.annotation.Log;
import com.tongji.common.core.domain.R;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.enums.BusinessType;
import com.tongji.common.utils.StringUtils;
import com.tongji.system.entity.SysDictData;
import com.tongji.system.dto.req.dict.SysDictDataAddReq;
import com.tongji.system.dto.req.dict.SysDictDataEditReq;
import com.tongji.system.dto.req.dict.SysDictDataListReq;
import com.tongji.system.service.SysDictDataService;
import com.tongji.system.service.SysDictTypeService;
import com.tongji.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息
 */
@Tag(name = "字典数据管理", description = "管理系统中的字典数据信息")
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private SysDictTypeService sysDictTypeService;

    @Operation(summary = "获取字典数据列表", description = "根据查询条件获取字典数据列表，支持分页")
    @PreAuthorize("@ss.hasPermi('system:dict:list')")
    @GetMapping("/listPage")
    public R<TablePage<SysDictData>> listPage(@Parameter(description = "查询参数") SysDictDataListReq listReq) {
        return R.successByData(sysDictDataService.selectDictDataListPage(listReq));
    }

    // TODO: excel
//    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
//    @PreAuthorize("@ss.hasPermi('system:dict:export')")
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysDictData dictData)
//    {
//        List<SysDictData> list = dictDataService.selectDictDataList(dictData);
//        ExcelUtil<SysDictData> util = new ExcelUtil<>(SysDictData.class);
//        util.exportExcel(response, list, "字典数据");
//    }

    /**
     * 查询字典数据详细
     */
    @Operation(summary = "获取字典数据详情", description = "根据字典编码获取字典数据的详细信息")
    @PreAuthorize("@ss.hasPermi('system:dict:query')")
    @GetMapping(value = "/{dictCode}")
    public R<SysDictData> getInfo(@Parameter(description = "字典编码") @PathVariable Long dictCode) {
        return successByData(sysDictDataService.selectDictDataById(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */
    @Operation(summary = "根据字典类型查询字典数据", description = "获取指定字典类型的所有字典数据")
    @GetMapping(value = "/type/{dictType}")
    public R<List<SysDictData>> dictType(@Parameter(description = "字典类型") @PathVariable String dictType) {
        List<SysDictData> data = sysDictTypeService.selectDictDataByType(dictType);
        if (StringUtils.isNull(data)) {
            data = new ArrayList<>();
        }
        return successByData(data);
    }

    /**
     * 新增字典类型
     */
    @Operation(summary = "新增字典数据", description = "添加新的字典数据项")
    @PreAuthorize("@ss.hasPermi('system:dict:add')")
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Object> add(@Parameter(description = "新增字典数据信息") @Validated @RequestBody SysDictDataAddReq addReq) {
        return toAjax(sysDictDataService.insertDictData(addReq, getLoginUser()));
    }

    /**
     * 修改保存字典类型
     */
    @Operation(summary = "修改字典数据", description = "更新已有的字典数据信息")
    @PreAuthorize("@ss.hasPermi('system:dict:edit')")
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Object> edit(@Parameter(description = "修改字典数据信息") @Validated @RequestBody SysDictDataEditReq editReq) {
        return toAjax(sysDictDataService.updateDictData(editReq, getLoginUser()));
    }

    /**
     * 删除字典类型
     */
    @Operation(summary = "删除字典数据", description = "根据字典编码数组批量删除字典数据")
    @PreAuthorize("@ss.hasPermi('system:dict:remove')")
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public R<Object> remove(@Parameter(description = "字典编码数组") @PathVariable List<Long> dictCodes) {
        sysDictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }
}
