package com.tongji.web.service;

import com.tongji.common.constant.CacheConstants;
import com.tongji.common.constant.Constants;
import com.tongji.common.constant.UserConstants;
import com.tongji.common.core.redis.RedisCache;
import com.tongji.common.exception.user.CaptchaException;
import com.tongji.common.exception.user.CaptchaExpireException;
import com.tongji.common.utils.MessageUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.manager.AsyncManager;
import com.tongji.manager.factory.AsyncFactory;
import com.tongji.security.utils.SecurityUtils;
import com.tongji.system.entity.SysUser;
import com.tongji.web.dto.req.RegisterReq;

import com.tongji.system.service.SysConfigService;
import com.tongji.system.service.SysUserService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 注册校验方法
 */
@Component
public class SysRegisterService {

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private RedisCache redisCache;

    /**
     * 注册
     */
    public String register(RegisterReq registerBody) {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        SysUser sysUser = new SysUser();
        sysUser.setUsername(username);

        // 验证码开关
        boolean captchaEnabled = sysConfigService.queryCaptchaEnabled();
        if (captchaEnabled) {
            validateCaptcha(registerBody.getCode(), registerBody.getUuid());
        }

        if (StringUtils.isEmpty(username)) {
            msg = "用户名不能为空";
        } else if (StringUtils.isEmpty(password)) {
            msg = "用户密码不能为空";
        } else if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            msg = "账户长度必须在2到20个字符之间";
        } else if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            msg = "密码长度必须在5到20个字符之间";
        } else if (!sysUserService.checkUsernameUnique(null, sysUser.getUsername())) {
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        } else {
            sysUser.setNickName(username);
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            boolean regFlag = sysUserService.registerUser(sysUser);
            if (!regFlag) {
                msg = "注册失败,请联系系统管理人员";
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }
        return msg;
    }

    /**
     * 校验验证码
     *
     * @param code     验证码
     * @param uuid     唯一标识
     */
    public void validateCaptcha(String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }
    }
}
