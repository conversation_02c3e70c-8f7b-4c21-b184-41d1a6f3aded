package com.tongji.config;

import com.tongji.logic.task.DataTask;
import jakarta.annotation.Resource;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Timer;
import java.util.TimerTask;

@Component
public class TaskConfig implements ApplicationRunner {

    @Resource
    private DataTask dataTask;

    private final Timer taskAwakeTimer = new Timer();

    @Override
    public void run(ApplicationArguments args) {
        dataTask.start();

        taskAwakeTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                dataTask.awake();
            }
        }, 0, 5000);
    }

}
