package com.tongji.config;

import com.tongji.common.config.AppConfig;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import jakarta.annotation.Resource;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger3的接口配置
 */
@Configuration
public class SwaggerConfig {

    @Value("${token.header}")
    private String token;

    /**
     * 系统基础配置
     */
    @Resource
    private AppConfig appConfig;

    /**
     * 自定义的 OpenAPI 对象
     */
    @Bean
    public OpenAPI customOpenApi() {
        return new OpenAPI().components(new Components()
                        // 设置认证的请求头
                        .addSecuritySchemes("apikey", securityScheme()))
                .addSecurityItem(new SecurityRequirement().addList("apikey"))
                .info(getApiInfo());
    }

    @Bean
    public SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.APIKEY)
                .name(token)
                .in(SecurityScheme.In.HEADER)
                .scheme("Bearer");
    }

    @Bean
    public GlobalOpenApiCustomizer globalOpenApiCustomizer() {

        return openAPI -> {

            if (openAPI.getPaths() != null) {
                openAPI.getPaths().forEach((s, pathItem) -> {
                    pathItem.readOperations().forEach(operation -> {
                        operation.addParametersItem(new io.swagger.v3.oas.models.parameters.HeaderParameter()
                                .name(token)
                                .description("token")
                                .required(true)
                                .schema(new io.swagger.v3.oas.models.media.StringSchema()));
                    });
                });
            }
        };
    }

    /**
     * 添加摘要信息
     */
    public Info getApiInfo() {
        return new Info()
                // 设置标题
                .title("标题：管理系统_接口文档")
                // 描述
                .description("描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...")
                // 作者信息
                .contact(new Contact().name(appConfig.getName()))
                // 版本
                .version("版本号:" + appConfig.getVersion());
    }
}
