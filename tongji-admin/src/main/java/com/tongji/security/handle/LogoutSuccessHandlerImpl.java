package com.tongji.security.handle;

import com.alibaba.fastjson2.JSON;
import com.tongji.common.constant.Constants;
import com.tongji.common.core.domain.R;
import com.tongji.common.utils.MessageUtils;
import com.tongji.common.utils.ServletUtils;
import com.tongji.common.utils.StringUtils;
import com.tongji.manager.AsyncManager;
import com.tongji.manager.factory.AsyncFactory;
import com.tongji.security.model.LoginUser;
import com.tongji.web.service.TokenService;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import java.io.IOException;

/**
 * 自定义退出处理类 返回成功
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {

    @Resource
    private TokenService tokenService;

    /**
     * 退出处理
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGOUT, MessageUtils.message("user.logout.success")));
        }
        ServletUtils.renderString(response, JSON.toJSONString(R.successByMsg(MessageUtils.message("user.logout.success"))));
    }
}
