package com.tongji.common.core.domain;

import com.tongji.common.constant.HttpStatus;
import com.tongji.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 操作消息提醒
 */
@Getter
public class R<T> {

    @Schema(description = "状态码")
    private final Integer code;

    @Schema(description = "消息内容")
    private final String msg;

    @Schema(description = "数据对象")
    private T data;

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg  消息内容
     * @param data 数据对象
     */
    private R(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        if (StringUtils.isNotNull(data)) {
            this.data = data;
        }
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static <T> R<T> success() {
        return R.successByMsg("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static <T> R<T> successByData(T data) {
        return R.success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 消息内容
     * @return 成功消息
     */
    public static <T> R<T> successByMsg(String msg) {
        return R.success(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg  消息内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static <T> R<T> success(String msg, T data) {
        return new R<>(HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回警告消息
     *
     * @param msg 消息内容
     * @return 警告消息
     */
    public static <T> R<T> warnByMsg(String msg) {
        return R.warn(msg, null);
    }

    /**
     * 返回警告消息
     *
     * @param msg  消息内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static <T> R<T> warn(String msg, T data) {
        return new R<>(HttpStatus.WARN, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static <T> R<T> error() {
        return R.errorByMsg("操作失败");
    }

    /**
     * 返回错误消息
     *
     * @param msg 消息内容
     * @return 错误消息
     */
    public static <T> R<T> errorByMsg(String msg) {
        return R.error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg  消息内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static <T> R<T> error(String msg, T data) {
        return new R<>(HttpStatus.ERROR, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg  消息内容
     * @return 错误消息
     */
    public static <T> R<T> error(int code, String msg) {
        return new R<>(code, msg, null);
    }

    /**
     * 返回错误消息
     * @param code 状态码
     * @param msg 消息内容
     * @param data 返回数据
     * @return 错误消息
     */
    public static <T> R<T> error(int code, String msg, T data) {
        return new R<>(code, msg, data);
    }

}
