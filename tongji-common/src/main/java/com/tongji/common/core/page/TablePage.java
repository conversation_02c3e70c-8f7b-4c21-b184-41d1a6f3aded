package com.tongji.common.core.page;

import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 表格分页数据对象
 */
@Data
@Schema(description = "表格分页数据对象")
public class TablePage<T> implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "当前页码")
    private int pageNum;

    @Schema(description = "每页显示记录数")
    private int pageSize;

    @Schema(description = "当前页记录数")
    private int size;

    @Schema(description = "总记录数")
    private long total;

    @Schema(description = "当前页开始行号")
    private long startRow;

    @Schema(description = "当前页结束行号")
    private long endRow;

    @Schema(description = "总页数")
    private int pages;

    @Schema(description = "前一页页码")
    private int prePage;

    @Schema(description = "下一页页码")
    private int nextPage;

    @Schema(description = "是否为第一页")
    private boolean isFirstPage;

    @Schema(description = "是否为最后一页")
    private boolean isLastPage;

    @Schema(description = "是否有前一页")
    private boolean hasPreviousPage;

    @Schema(description = "是否有下一页")
    private boolean hasNextPage;

    @Schema(description = "分页数据列表")
    private List<T> rows;

    public <M> TablePage<M> convert(List<M> rows) {
        TablePage<M> tablePage = new TablePage<>();
        tablePage.setPageNum(pageNum);
        tablePage.setPageSize(pageSize);
        tablePage.setSize(size);
        tablePage.setStartRow(startRow);
        tablePage.setEndRow(endRow);
        tablePage.setPages(pages);
        tablePage.setPrePage(prePage);
        tablePage.setNextPage(nextPage);
        tablePage.setFirstPage(isFirstPage);
        tablePage.setLastPage(isLastPage);
        tablePage.setHasPreviousPage(hasPreviousPage);
        tablePage.setHasNextPage(hasNextPage);
        tablePage.setTotal(total);
        tablePage.setRows(rows);
        return tablePage;
    }

    private static <M, T> void push(PageInfo<M> info, TablePage<T> tablePage) {
        tablePage.setPageNum(info.getPageNum());
        tablePage.setPageSize(info.getPageSize());
        tablePage.setSize(info.getSize());
        tablePage.setStartRow(info.getStartRow());
        tablePage.setEndRow(info.getEndRow());
        tablePage.setPages(info.getPages());
        tablePage.setPrePage(info.getPrePage());
        tablePage.setNextPage(info.getNextPage());
        tablePage.setFirstPage(info.isIsFirstPage());
        tablePage.setLastPage(info.isIsLastPage());
        tablePage.setHasPreviousPage(info.isHasPreviousPage());
        tablePage.setHasNextPage(info.isHasNextPage());
        tablePage.setTotal(info.getTotal());
    }

    public static <T> TablePage<T> from(PageInfo<T> info) {
        TablePage<T> tablePage = new TablePage<>();
        push(info, tablePage);
        tablePage.setRows(info.getList());

        return tablePage;
    }

    public static <M, T> TablePage<T> from(PageInfo<M> info, List<T> rows) {
        TablePage<T> tablePage = new TablePage<>();
        push(info, tablePage);
        tablePage.setRows(rows);

        return tablePage;
    }

    public static <T> TablePage<T> empty() {
        TablePage<T> tablePage = new TablePage<>();

        tablePage.setPageNum(0);
        tablePage.setPageSize(0);
        tablePage.setSize(0);
        tablePage.setStartRow(0);
        tablePage.setEndRow(0);
        tablePage.setPages(0);
        tablePage.setPrePage(0);
        tablePage.setNextPage(0);
        tablePage.setFirstPage(false);
        tablePage.setLastPage(false);
        tablePage.setHasPreviousPage(false);
        tablePage.setHasNextPage(false);
        tablePage.setTotal(0L);

        tablePage.setRows(new ArrayList<>());

        return tablePage;
    }
}
