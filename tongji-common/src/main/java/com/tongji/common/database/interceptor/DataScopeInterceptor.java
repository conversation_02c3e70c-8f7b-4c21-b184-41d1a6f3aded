package com.tongji.common.database.interceptor;

import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.lang.reflect.Field;

//@Component
//@Intercepts({
//        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
//        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
//})
//@Intercepts({
//        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class}),
//})
public class DataScopeInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        System.out.println("-------------------DataScopeInterceptor-------------------");

        // executor
//        MappedStatement statement = (MappedStatement) invocation.getArgs()[0];
//        System.out.println(statement.getId());
//        BoundSql boundSql = statement.getBoundSql(invocation.getArgs()[1]);
//        String sql = boundSql.getSql();
//
//        Field field = boundSql.getClass().getDeclaredField("sql");
//        field.setAccessible(true);
//        System.out.println(sql.replaceAll("!\\[pageSeg]", "limit 1"));
//        field.set(boundSql, sql.replaceAll("!\\[pageSeg]", "limit 1"));
//        return invocation.proceed();

        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = MetaObject.forObject(
                statementHandler,
                SystemMetaObject.DEFAULT_OBJECT_FACTORY,
                SystemMetaObject.DEFAULT_OBJECT_WRAPPER_FACTORY,
                new DefaultReflectorFactory()
        );
        MappedStatement statement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        System.out.println(statement.getId());

        BoundSql boundSql = statementHandler.getBoundSql();
        String sql = boundSql.getSql();
        Field field = boundSql.getClass().getDeclaredField("sql");
        field.setAccessible(true);
        System.out.println(sql.replaceAll("!\\[pageSeg]", "limit 1"));
        field.set(boundSql, sql.replaceAll("!\\[pageSeg]", "limit 1"));

        return invocation.proceed();
    }

}
