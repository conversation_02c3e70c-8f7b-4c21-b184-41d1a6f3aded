package com.tongji.common.database.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.util.List;
import java.util.function.Function;

public interface CoreMapper<T> extends BaseMapper<T> {

    default List<T> selectList() {
        return selectList(Wrappers.emptyWrapper());
    }

    default List<T> selectList(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<>());
        return selectList(wrapper);
    }

    default Long selectCount(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<>());
        return selectCount(wrapper);
    }

    default T selectFirst(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<>());
        wrapper.last(" limit 1 ");
        return selectOne(wrapper);
    }

    default T selectOne(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<>());
        return selectOne(wrapper);
    }

    default int delete(Function<LambdaQueryWrapper<T>, LambdaQueryWrapper<T>> function) {
        LambdaQueryWrapper<T> wrapper = function.apply(new LambdaQueryWrapper<>());
        return delete(wrapper);
    }

    default int update(T entity, Function<LambdaUpdateWrapper<T>, LambdaUpdateWrapper<T>> function) {
        LambdaUpdateWrapper<T> wrapper = function.apply(new LambdaUpdateWrapper<>());
        return update(entity, wrapper);
    }

    default int update(Function<LambdaUpdateWrapper<T>, LambdaUpdateWrapper<T>> function) {
        LambdaUpdateWrapper<T> wrapper = function.apply(new LambdaUpdateWrapper<>());
        return update(null, wrapper);
    }

}
