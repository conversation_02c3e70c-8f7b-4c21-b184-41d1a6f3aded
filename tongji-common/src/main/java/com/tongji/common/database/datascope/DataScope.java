package com.tongji.common.database.datascope;

import com.tongji.common.utils.spring.SpringUtils;

public class DataScope {

    public static String from(String deptAlias, String userAlias) {
        try {
            DataScopeHandler aware = SpringUtils.getBean(DataScopeHandler.class);
            return aware.from(dept<PERSON>lia<PERSON>, userAlias);
        } catch (Exception e) {
            return "";
        }
    }

    public static String fromPermission(String deptAlias, String userAlias, String permission) {
        try {
            DataScopeHandler aware = SpringUtils.getBean(DataScopeHandler.class);
            return aware.fromPermission(deptAlias, userAlias, permission);
        } catch (Exception e) {
            return "";
        }
    }

    public static String fromDept(String deptAlias) {
        try {
            DataScopeHandler aware = SpringUtils.getBean(DataScopeHandler.class);
            return aware.fromDept(deptAlias);
        } catch (Exception e) {
            return "";
        }
    }

    public static String fromUser(String userAlias) {
        try {
            DataScopeHandler aware = SpringUtils.getBean(DataScopeHandler.class);
            return aware.fromUser(userAlias);
        } catch (Exception e) {
            return "";
        }
    }

}
