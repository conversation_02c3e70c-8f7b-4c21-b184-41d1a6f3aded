package com.tongji.common.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tongji.common.core.page.PageDomain;
import com.tongji.common.core.page.TablePage;
import com.tongji.common.core.page.TableSupport;
import com.tongji.common.utils.sql.SqlUtil;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 分页工具类
 */
public class PageUtils extends PageHelper {

    public static <E, T extends List<E>> T execute(Supplier<T> supplier, int pageNum, int pageSize, String orderBy, Boolean reasonable) {
        try(Page<E> page = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            page.setReasonable(reasonable);
            return supplier.get();
        }
    }

    public static <E, T extends List<E>> T execute(Supplier<T> supplier, int pageNum, int pageSize, String orderBy) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            return supplier.get();
        }
    }

    public static <E, T extends List<E>> T execute(Supplier<T> supplier, int pageNum, int pageSize) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize)) {
            return supplier.get();
        }
    }

    public static <E, T extends List<E>> T execute(Supplier<T> supplier) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        try(Page<E> page = PageHelper.startPage(
                pageDomain.getPageNum(),
                pageDomain.getPageSize(),
                SqlUtil.escapeOrderBySql(pageDomain.getOrderBy()))
        ) {
            page.setReasonable(pageDomain.getReasonable());
            return supplier.get();
        }
    }

    public static <E> TablePage<E> paginate(Supplier<List<E>> supplier, int pageNum, int pageSize, String orderBy, Boolean reasonable) {
        try(Page<E> page = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            page.setReasonable(reasonable);
            return TablePage.from(new PageInfo<>(supplier.get()));
        }
    }

    public static <E> TablePage<E> paginate(Supplier<List<E>> supplier, int pageNum, int pageSize, String orderBy) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            return TablePage.from(new PageInfo<>(supplier.get()));
        }
    }

    public static <E> TablePage<E> paginate(Supplier<List<E>> supplier, int pageNum, int pageSize) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize)) {
            return TablePage.from(new PageInfo<>(supplier.get()));
        }
    }

    public static <E> TablePage<E> paginate(Supplier<List<E>> supplier) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        try(Page<E> page = PageHelper.startPage(
                pageDomain.getPageNum(),
                pageDomain.getPageSize(),
                SqlUtil.escapeOrderBySql(pageDomain.getOrderBy()))
        ) {
            page.setReasonable(pageDomain.getReasonable());
            return TablePage.from(new PageInfo<>(supplier.get()));
        }
    }

    public static <E, T> TablePage<T> paginate(Supplier<List<E>> supplier, Function<E, T> function, int pageNum, int pageSize, String orderBy, Boolean reasonable) {
        try(Page<E> page = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            page.setReasonable(reasonable);

            List<E> list = supplier.get();
            return TablePage.from(new PageInfo<>(list), list.stream().map(function).toList());
        }
    }

    public static <E, T> TablePage<T> paginate(Supplier<List<E>> supplier, Function<E, T> function, int pageNum, int pageSize, String orderBy) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize, SqlUtil.escapeOrderBySql(orderBy))) {
            List<E> list = supplier.get();
            return TablePage.from(new PageInfo<>(list), list.stream().map(function).toList());
        }
    }

    public static <E, T> TablePage<T> paginate(Supplier<List<E>> supplier, Function<E, T> function, int pageNum, int pageSize) {
        try(Page<E> ignored = PageHelper.startPage(pageNum, pageSize)) {
            List<E> list = supplier.get();
            return TablePage.from(new PageInfo<>(list), list.stream().map(function).toList());
        }
    }

    public static <E, T> TablePage<T> paginate(Supplier<List<E>> supplier, Function<E, T> function) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        try(Page<E> page = PageHelper.startPage(
                pageDomain.getPageNum(),
                pageDomain.getPageSize(),
                SqlUtil.escapeOrderBySql(pageDomain.getOrderBy()))
        ) {
            page.setReasonable(pageDomain.getReasonable());

            List<E> list = supplier.get();
            return TablePage.from(new PageInfo<>(list), list.stream().map(function).toList());
        }
    }

}
