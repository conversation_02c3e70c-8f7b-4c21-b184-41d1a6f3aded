package com.tongji.quartz.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "任务查询请求")
public class JobQueryReq {

    @Schema(description = "任务名称", example = "数据备份任务")
    private String jobName;

    @Schema(description = "任务组名", example = "系统任务")
    private String jobGroup;

    @Schema(description = "调用目标字符串", example = "backupTask.execute()")
    private String invokeTarget;

    @Schema(description = "任务状态（0正常 1暂停）", example = "0")
    private String status;
}
