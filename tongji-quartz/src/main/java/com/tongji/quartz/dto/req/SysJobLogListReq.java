package com.tongji.quartz.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "任务日志查询请求")
public class SysJobLogListReq {

    @Schema(description = "任务名称", example = "数据备份任务")
    private String jobName;

    @Schema(description = "任务组名", example = "系统任务")
    private String jobGroup;

    @Schema(description = "调用目标字符串", example = "backupTask.execute()")
    private String invokeTarget;

    @Schema(description = "日志状态（0正常 1失败）", example = "0")
    private String status;

    @Schema(description = "开始创建时间", example = "2023-01-01 00:00:00")
    private String beginCreateTime;

    @Schema(description = "结束创建时间", example = "2023-12-31 23:59:59")
    private String endCreateTime;
}
