package com.tongji.quartz.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
@Schema(description = "添加任务请求")
public class JobAddReq {

    @Schema(description = "任务名称", example = "数据备份任务")
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 1, max = 64, message = "任务名称不能超过64个字符")
    private String jobName;

    @Schema(description = "任务组名", example = "系统任务")
    @NotBlank(message = "任务组名不能为空")
    private String jobGroup;

    @Schema(description = "调用目标字符串", example = "backupTask.execute()")
    @NotBlank(message = "调用目标字符串不能为空")
    @Size(min = 1, max = 500, message = "调用目标字符串长度不能超过500个字符")
    private String invokeTarget;

    @Schema(description = "Cron执行表达式", example = "0 0 2 * * ?")
    @NotBlank(message = "Cron执行表达式不能为空")
    @Size(min = 1, max = 255, message = "Cron执行表达式不能超过255个字符")
    private String cronExpression;

    @Schema(description = "计划执行错误策略", example = "1")
    private String misfirePolicy;

    @Schema(description = "是否并发执行（0允许 1禁止）", example = "0")
    private String concurrent;
}