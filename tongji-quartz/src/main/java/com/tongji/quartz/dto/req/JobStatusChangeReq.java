package com.tongji.quartz.dto.req;

import jakarta.validation.constraints.NotNull;

public class JobStatusChangeReq {

    @NotNull(message = "任务ID不能为空")
    private Long jobId;

    @NotNull(message = "任务状态不能为空")
    private String status;

    // getter 和 setter 方法

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}