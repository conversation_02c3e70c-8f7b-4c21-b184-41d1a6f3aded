package com.tongji.quartz.mapper;

import com.tongji.common.database.mapper.CoreMapper;
import com.tongji.quartz.entity.SysJobLog;
import com.tongji.quartz.dto.req.SysJobLogListReq;

import java.util.List;

/**
 * 调度任务日志信息 数据层
 */
public interface SysJobLogMapper extends CoreMapper<SysJobLog> {

    /**
     * 获取quartz调度器日志的计划任务
     * @return 调度任务日志集合
     */
    List<SysJobLog> selectJobLogList(SysJobLogListReq req);

    /**
     * 查询所有调度任务日志
     *
     * @return 调度任务日志列表
     */
    List<SysJobLog> selectJobLogAll();

    /**
     * 通过调度任务日志ID查询调度信息
     *
     * @param jobLogId 调度任务日志ID
     * @return 调度任务日志对象信息
     */
    SysJobLog selectJobLogById(Long jobLogId);

    /**
     * 新增任务日志
     *
     * @param jobLog 调度日志信息
     * @return 结果
     */
    int insertJobLog(SysJobLog jobLog);

    /**
     * 批量删除调度日志信息
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    int deleteJobLogByIds(List<Long> logIds);

    /**
     * 删除任务日志
     *
     * @param jobId 调度日志ID
     * @return 结果
     */
    int deleteJobLogById(Long jobId);

    /**
     * 清空任务日志
     */
    void cleanJobLog();
}
