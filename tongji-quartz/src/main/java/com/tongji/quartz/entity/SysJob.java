package com.tongji.quartz.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tongji.common.constant.ScheduleConstants;
import com.tongji.common.core.domain.BaseEntity;
import com.tongji.common.utils.StringUtils;
import com.tongji.quartz.util.CronUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 定时任务调度表 sys_job
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "定时任务调度信息")
public class SysJob extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "任务ID")
    private Long jobId;

    @Schema(description = "任务名称")
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 1, max = 64, message = "任务名称不能超过64个字符")
    private String jobName;

    @Schema(description = "任务组名")
    private String jobGroup;

    @Schema(description = "调用目标字符串")
    @NotBlank(message = "调用目标字符串不能为空")
    @Size(min = 1, max = 500, message = "调用目标字符串长度不能超过500个字符")
    private String invokeTarget;

    @Schema(description = "cron执行表达式")
    @NotBlank(message = "Cron执行表达式不能为空")
    @Size(min = 1, max = 255, message = "Cron执行表达式不能超过255个字符")
    private String cronExpression;

    @Schema(description = "cron计划策略：0=默认，1=立即触发执行，2=触发一次执行，3=不触发立即执行")
    private String misfirePolicy = ScheduleConstants.MISFIRE_DEFAULT;

    @Schema(description = "是否并发执行：0=允许，1=禁止")
    private String concurrent;

    @Schema(description = "任务状态：0=正常，1=暂停")
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "下一次执行时间", accessMode = Schema.AccessMode.READ_ONLY)
    // @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    public Date getNextValidTime() {
        if (StringUtils.isNotEmpty(cronExpression)) {
            return CronUtils.getNextExecution(cronExpression);
        }
        return null;
    }

}
