package com.tongji.quartz.service;

import com.tongji.common.core.page.TablePage;
import com.tongji.common.utils.PageUtils;
import com.tongji.quartz.entity.SysJobLog;
import com.tongji.quartz.dto.req.SysJobLogListReq;
import com.tongji.quartz.mapper.SysJobLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定时任务调度日志信息 服务层
 */
@Service
public class SysJobLogService {

    @Resource
    private SysJobLogMapper sysJobLogMapper;

    /**
     * 获取quartz调度器日志的计划任务
     *
     * @param listReq 调度日志信息
     * @return 调度任务日志集合
     */
    public TablePage<SysJobLog> selectJobLogList(SysJobLogListReq listReq) {
        return PageUtils.paginate(() -> sysJobLogMapper.selectJobLogList(listReq));
    }

    /**
     * 通过调度任务日志ID查询调度信息
     *
     * @param jobLogId 调度任务日志ID
     * @return 调度任务日志对象信息
     */
    public SysJobLog selectJobLogById(Long jobLogId) {
        return sysJobLogMapper.selectJobLogById(jobLogId);
    }

    /**
     * 新增任务日志
     *
     * @param jobLog 调度日志信息
     */
    public void addJobLog(SysJobLog jobLog) {
        sysJobLogMapper.insertJobLog(jobLog);
    }

    /**
     * 批量删除调度日志信息
     *
     * @param logIds 需要删除的日志ID
     * @return 结果
     */
    public int deleteJobLogByIds(List<Long> logIds) {
        return sysJobLogMapper.deleteJobLogByIds(logIds);
    }

    /**
     * 删除任务日志
     *
     * @param jobId 调度日志ID
     * @return 结果
     */
    public int deleteJobLogById(Long jobId) {
        return sysJobLogMapper.deleteJobLogById(jobId);
    }

    /**
     * 清空任务日志
     */
    public void cleanJobLog() {
        sysJobLogMapper.cleanJobLog();
    }
}
